import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Approval Mode Selection Component
 *
 * Interactive interface for selecting command approval policies
 * Provides detailed explanations and security warnings
 */
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
const APPROVAL_MODES = [
    {
        mode: 'suggest',
        title: 'Suggest Mode',
        description: 'AI suggests commands but requires manual approval for execution',
        security: 'high',
        details: [
            '• All commands require explicit user approval',
            '• Shows command details before execution',
            '• Safest option for production environments',
            '• Allows review of potentially dangerous operations'
        ],
        examples: [
            'AI suggests: rm -rf temp/',
            'User sees preview and can approve/deny',
            'Perfect for learning and safety'
        ]
    },
    {
        mode: 'auto-edit',
        title: 'Auto-Edit Mode',
        description: 'Automatically executes safe commands, asks for approval on risky ones',
        security: 'medium',
        details: [
            '• Safe commands execute automatically (ls, cat, grep)',
            '• Risky commands require approval (rm, chmod, sudo)',
            '• Balanced approach for experienced users',
            '• Configurable safe command list'
        ],
        examples: [
            'ls, cat, grep → Auto-execute',
            'rm, sudo, chmod → Ask approval',
            'Good for development workflows'
        ]
    },
    {
        mode: 'full-auto',
        title: 'Full Auto Mode',
        description: 'Executes all AI-suggested commands automatically without approval',
        security: 'low',
        details: [
            '• All commands execute immediately',
            '• Maximum automation and speed',
            '• Requires high trust in AI decisions',
            '• Use only in controlled environments'
        ],
        examples: [
            'All commands execute instantly',
            'No interruptions or confirmations',
            'Use with caution!'
        ]
    }
];
export function ApprovalSelector({ currentMode, onSelect, onCancel }) {
    const [selectedIndex, setSelectedIndex] = useState(APPROVAL_MODES.findIndex(mode => mode.mode === currentMode));
    // Handle keyboard input
    useInput((input, key) => {
        if (key.escape) {
            onCancel();
            return;
        }
        if (key.return) {
            const selectedMode = APPROVAL_MODES[selectedIndex];
            if (selectedMode) {
                onSelect(selectedMode.mode);
            }
            return;
        }
        if (key.upArrow) {
            setSelectedIndex(prev => Math.max(0, prev - 1));
            return;
        }
        if (key.downArrow) {
            setSelectedIndex(prev => Math.min(APPROVAL_MODES.length - 1, prev + 1));
            return;
        }
    });
    const getSecurityColor = (security) => {
        switch (security) {
            case 'high': return 'green';
            case 'medium': return 'yellow';
            case 'low': return 'red';
            default: return 'gray';
        }
    };
    const getSecurityIcon = (security) => {
        switch (security) {
            case 'high': return '🛡️';
            case 'medium': return '⚠️';
            case 'low': return '⚡';
            default: return '❓';
        }
    };
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Box, { borderStyle: "single", paddingX: 1, marginBottom: 1, children: _jsx(Text, { bold: true, color: "blue", children: "Command Approval Mode Selection" }) }), _jsx(Box, { marginBottom: 1, children: _jsxs(Text, { children: ["Current mode: ", _jsx(Text, { bold: true, color: "green", children: currentMode })] }) }), _jsx(Box, { flexDirection: "column", children: APPROVAL_MODES.map((modeInfo, index) => {
                    const isSelected = index === selectedIndex;
                    const isCurrent = modeInfo.mode === currentMode;
                    return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsx(Box, { children: _jsxs(Text, { color: isSelected ? 'cyan' : undefined, children: [isSelected ? '▶ ' : '  ', _jsx(Text, { bold: true, children: modeInfo.title }), isCurrent && _jsx(Text, { color: "green", children: " (current)" })] }) }), _jsx(Box, { marginLeft: 2, children: _jsxs(Text, { color: getSecurityColor(modeInfo.security), children: [getSecurityIcon(modeInfo.security), " Security: ", modeInfo.security.toUpperCase()] }) }), _jsx(Box, { marginLeft: 2, marginTop: 1, children: _jsx(Text, { color: "gray", children: modeInfo.description }) }), isSelected && (_jsxs(Box, { flexDirection: "column", marginLeft: 2, marginTop: 1, children: [_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { bold: true, color: "cyan", children: "Details:" }), modeInfo.details.map((detail, i) => (_jsx(Text, { color: "gray", children: detail }, i)))] }), _jsxs(Box, { flexDirection: "column", marginTop: 1, children: [_jsx(Text, { bold: true, color: "cyan", children: "Examples:" }), modeInfo.examples.map((example, i) => (_jsxs(Text, { color: "yellow", children: ["\u2022 ", example] }, i)))] }), modeInfo.security === 'low' && (_jsx(Box, { marginTop: 1, borderStyle: "single", paddingX: 1, children: _jsx(Text, { color: "red", bold: true, children: "\u26A0\uFE0F WARNING: This mode executes commands without approval!" }) }))] }))] }, modeInfo.mode));
                }) }), _jsx(Box, { borderStyle: "single", paddingX: 1, marginTop: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "gray", children: "\u2191/\u2193 Navigate \u2022 Enter Select \u2022 Esc Cancel" }), _jsxs(Text, { color: "yellow", children: ["Choose approval mode: ", APPROVAL_MODES[selectedIndex]?.title] })] }) }), _jsxs(Box, { flexDirection: "column", marginTop: 1, borderStyle: "single", paddingX: 1, children: [_jsx(Text, { bold: true, color: "blue", children: "Security Recommendations:" }), _jsx(Text, { color: "green", children: "\uD83D\uDEE1\uFE0F Suggest: Best for learning and production" }), _jsx(Text, { color: "yellow", children: "\u26A0\uFE0F Auto-Edit: Good for development workflows" }), _jsx(Text, { color: "red", children: "\u26A1 Full-Auto: Use only in controlled environments" })] })] }));
}
//# sourceMappingURL=approval-selector.js.map