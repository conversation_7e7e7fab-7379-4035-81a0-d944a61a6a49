{"version": 3, "file": "history-browser.js", "sourceRoot": "", "sources": ["../../../src/components/ui/history-browser.tsx"], "names": [], "mappings": ";AAAA;;;;;GAKG;AAEH,OAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACnD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAE1C,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,wCAAwC,CAAC;AAQpG,MAAM,UAAU,cAAc,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAuB;IACxE,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAiB,EAAE,CAAC,CAAC;IAC3D,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,QAAQ,CAAiB,EAAE,CAAC,CAAC;IAC3E,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAS,CAAC,CAAC,CAAC;IAC9D,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAS,EAAE,CAAC,CAAC;IAC3D,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAsB,QAAQ,CAAC,CAAC;IAChE,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAM,IAAI,CAAC,CAAC;IAE9C,wBAAwB;IACxB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,WAAW,GAAG,GAAG,EAAE;YACvB,MAAM,WAAW,GAAG,UAAU,EAAE,CAAC;YACjC,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;YAEvC,oCAAoC;YACpC,MAAM,eAAe,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,OAAO,EAAE,CAAC;YAEnD,UAAU,CAAC,eAAe,CAAC,CAAC;YAC5B,kBAAkB,CAAC,eAAe,CAAC,CAAC;YACpC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACzB,CAAC,CAAC;QAEF,WAAW,EAAE,CAAC;IAChB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,gBAAgB;IAChB,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC;YACvB,MAAM,OAAO,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;YAC3C,kBAAkB,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3C,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAC5B,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;IAE3B,wBAAwB;IACxB,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,IAAI,KAAK,QAAQ,IAAI,WAAW,EAAE,CAAC;gBACrC,cAAc,CAAC,EAAE,CAAC,CAAC;gBACnB,OAAO,CAAC,QAAQ,CAAC,CAAC;YACpB,CAAC;iBAAM,CAAC;gBACN,QAAQ,EAAE,CAAC;YACb,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC,CAAC;YACrD,IAAI,aAAa,EAAE,CAAC;gBAClB,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAClC,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YAChD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;YAClB,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YACzE,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9B,OAAO,CAAC,QAAQ,CAAC,CAAC;YAClB,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9B,QAAQ,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACtB,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC;YACD,OAAO;QACT,CAAC;QAED,sBAAsB;QACtB,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACzD,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;QACvC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,eAAe,GAAG,CAAC,SAAiB,EAAU,EAAE;QACpD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QACjC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAE5D,IAAI,QAAQ,GAAG,CAAC;YAAE,OAAO,UAAU,CAAC;QACpC,IAAI,QAAQ,GAAG,EAAE;YAAE,OAAO,GAAG,QAAQ,OAAO,CAAC;QAC7C,IAAI,SAAS,GAAG,EAAE;YAAE,OAAO,GAAG,SAAS,OAAO,CAAC;QAC/C,IAAI,QAAQ,GAAG,CAAC;YAAE,OAAO,GAAG,QAAQ,OAAO,CAAC;QAE5C,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACnC,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,KAAmB,EAAU,EAAE;QACpD,IAAI,KAAK,CAAC,OAAO,KAAK,IAAI;YAAE,OAAO,GAAG,CAAC;QACvC,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK;YAAE,OAAO,GAAG,CAAC;QACxC,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,KAAmB,EAAU,EAAE;QACrD,IAAI,KAAK,CAAC,OAAO,KAAK,IAAI;YAAE,OAAO,OAAO,CAAC;QAC3C,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK;YAAE,OAAO,KAAK,CAAC;QAC1C,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aAEpC,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,YACpD,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAC,MAAM,wCAA+B,GAClD,EAGL,KAAK,IAAI,CACR,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,YAClB,MAAC,IAAI,0BACI,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,YAAE,KAAK,CAAC,aAAa,GAAQ,0BAC1C,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,YAAE,KAAK,CAAC,kBAAkB,GAAQ,sBACzD,KAAC,IAAI,IAAC,KAAK,EAAC,KAAK,YAAE,KAAK,CAAC,cAAc,GAAQ,sBAC/C,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,YAAE,KAAK,CAAC,cAAc,GAAQ,IACrD,GACH,CACP,EAGD,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,YACpD,MAAC,IAAI,eACF,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,cAC1B,KAAC,IAAI,IAAC,KAAK,EAAE,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,YACrD,WAAW,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,wBAAwB,CAAC,GAC/D,IACF,GACH,EAGN,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAE,EAAE,YACnC,eAAe,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAC9B,KAAC,GAAG,IAAC,cAAc,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,YAC5D,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,YACf,WAAW,CAAC,CAAC,CAAC,wCAAwC,CAAC,CAAC,CAAC,8BAA8B,GACnF,GACH,CACP,CAAC,CAAC,CAAC,CACF,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;oBAChD,MAAM,UAAU,GAAG,KAAK,KAAK,aAAa,CAAC;oBAC3C,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;oBACxC,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;oBAC1C,MAAM,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;oBAEnD,OAAO,CACL,MAAC,GAAG,IAAqC,aAAa,EAAC,QAAQ,aAC7D,KAAC,GAAG,cACF,MAAC,IAAI,IAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,aACzC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACzB,KAAC,IAAI,IAAC,KAAK,EAAE,WAAW,YAAG,UAAU,GAAQ,EAC5C,GAAG,EACJ,KAAC,IAAI,IAAC,IAAI,EAAE,UAAU,YAAG,KAAK,CAAC,OAAO,GAAQ,IACzC,GACH,EACL,UAAU,IAAI,CACb,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,YAChB,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,2BACL,SAAS,EACnB,KAAK,CAAC,OAAO,KAAK,SAAS,IAAI,CAC9B,MAAC,IAAI,IAAC,KAAK,EAAE,WAAW,aACrB,KAAK,EACL,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,IAChC,CACR,IACI,GACH,CACP,KArBO,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,EAAE,CAsBjC,CACP,CAAC;gBACJ,CAAC,CAAC,CACH,GACG,EAGL,eAAe,CAAC,MAAM,GAAG,EAAE,IAAI,CAC9B,KAAC,GAAG,IAAC,cAAc,EAAC,QAAQ,EAAC,SAAS,EAAE,CAAC,YACvC,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,yBACP,eAAe,CAAC,MAAM,GAAG,EAAE,sBAC/B,GACH,CACP,EAGA,eAAe,CAAC,aAAa,CAAC,IAAI,CACjC,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YACjD,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAC,MAAM,kCAAyB,EAChD,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,YAAE,eAAe,CAAC,aAAa,CAAC,CAAC,OAAO,GAAQ,IAChE,GACF,CACP,EAGD,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YACjD,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,mGAEX,EACN,IAAI,KAAK,QAAQ,IAAI,CACpB,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,mEAEb,CACR,EACA,eAAe,CAAC,aAAa,CAAC,IAAI,CACjC,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,yCACS,eAAe,CAAC,aAAa,CAAC,CAAC,OAAO,IAC1D,CACR,IACG,GACF,IACF,CACP,CAAC;AACJ,CAAC"}