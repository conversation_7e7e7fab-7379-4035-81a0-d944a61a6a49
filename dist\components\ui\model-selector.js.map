{"version": 3, "file": "model-selector.js", "sourceRoot": "", "sources": ["../../../src/components/ui/model-selector.tsx"], "names": [], "mappings": ";AAAA;;;;;GAKG;AAEH,OAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACnD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,KAAK,CAAC;AAClD,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,WAAW,EAAwB,MAAM,4BAA4B,CAAC;AAC/E,OAAO,EAAE,qBAAqB,EAAE,MAAM,0BAA0B,CAAC;AACjE,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAC;AAkBlD,MAAM,UAAU,aAAa,CAAC,EAC5B,eAAe,EACf,YAAY,EACZ,QAAQ,EACR,QAAQ,EACW;IACnB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,EAAE,CAAC;IAE1B,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,QAAQ,CAAS,eAAe,CAAC,CAAC;IAClF,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAS,YAAY,CAAC,CAAC;IACzE,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAA+B,EAAE,CAAC,CAAC;IAC7E,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,QAAQ,CAAuB,UAAU,CAAC,CAAC;IACnE,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAS,CAAC,CAAC,CAAC;IAC9D,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAS,CAAC,CAAC,CAAC;IAExD,uBAAuB;IACvB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,aAAa,GAAG,KAAK,IAAI,EAAE;YAC/B,MAAM,kBAAkB,GAAG,qBAAqB,EAAE,CAAC;YACnD,MAAM,YAAY,GAAiC,EAAE,CAAC;YAEtD,KAAK,MAAM,QAAQ,IAAI,kBAAkB,EAAE,CAAC;gBAC1C,MAAM,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;gBACxC,YAAY,CAAC,QAAQ,CAAC,GAAG;oBACvB,IAAI,EAAE,QAAQ;oBACd,SAAS;oBACT,MAAM,EAAE,EAAE;oBACV,OAAO,EAAE,KAAK;iBACf,CAAC;YACJ,CAAC;YAED,YAAY,CAAC,YAAY,CAAC,CAAC;YAE3B,6BAA6B;YAC7B,MAAM,YAAY,GAAG,kBAAkB,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;YACjE,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;gBACtB,gBAAgB,CAAC,YAAY,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC;QAEF,aAAa,EAAE,CAAC;IAClB,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;IAEtB,oCAAoC;IACpC,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,UAAU,GAAG,KAAK,IAAI,EAAE;YAC5B,IAAI,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,CAAC;gBACjE,OAAO;YACT,CAAC;YAED,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpB,GAAG,IAAI;gBACP,CAAC,gBAAgB,CAAC,EAAE;oBAClB,GAAG,IAAI,CAAC,gBAAgB,CAAC;oBACzB,OAAO,EAAE,IAAI;oBACb,KAAK,EAAE,SAAS;iBACjB;aACF,CAAC,CAAC,CAAC;YAEJ,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,gBAAgB,CAAC,CAAC;gBAEnD,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACpB,GAAG,IAAI;oBACP,CAAC,gBAAgB,CAAC,EAAE;wBAClB,GAAG,IAAI,CAAC,gBAAgB,CAAC;wBACzB,MAAM;wBACN,OAAO,EAAE,KAAK;qBACf;iBACF,CAAC,CAAC,CAAC;gBAEJ,0BAA0B;gBAC1B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtB,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;oBAClD,aAAa,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAEpD,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;wBACrB,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9B,CAAC;gBACH,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACpB,GAAG,IAAI;oBACP,CAAC,gBAAgB,CAAC,EAAE;wBAClB,GAAG,IAAI,CAAC,gBAAgB,CAAC;wBACzB,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,uBAAuB;qBACxE;iBACF,CAAC,CAAC,CAAC;YACN,CAAC;QACH,CAAC,CAAC;QAEF,UAAU,EAAE,CAAC;IACf,CAAC,EAAE,CAAC,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAC;IAErC,wBAAwB;IACxB,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,QAAQ,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;gBACxB,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7C,MAAM,QAAQ,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC;gBAE9C,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,SAAS,EAAE,CAAC;oBACnC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;oBAC9B,OAAO,CAAC,OAAO,CAAC,CAAC;oBACjB,aAAa,CAAC,CAAC,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,2BAA2B;gBAC3B,QAAQ,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;YAC5C,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;gBACrB,OAAO,CAAC,UAAU,CAAC,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACN,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,CAAC;gBACvD,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,SAAS,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7E,OAAO,CAAC,OAAO,CAAC,CAAC;gBACnB,CAAC;YACH,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;gBACxB,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;YAClB,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;gBACxB,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YAClF,CAAC;iBAAM,CAAC;gBACN,MAAM,MAAM,GAAG,SAAS,CAAC,gBAAgB,CAAC,EAAE,MAAM,IAAI,EAAE,CAAC;gBACzD,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YAC/D,CAAC;YACD,OAAO;QACT,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,yCAAyC;IACzC,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,aAAa,CAAC,aAAa,CAAC,EAAE,CAAC;YACjC,mBAAmB,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC,EAAE,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;IAE/B,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,MAAM,GAAG,SAAS,CAAC,gBAAgB,CAAC,EAAE,MAAM,IAAI,EAAE,CAAC;QACzD,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YACvB,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;QACvC,CAAC;IACH,CAAC,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAC,CAAC;IAE9C,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAE7C,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;YAC3C,MAAM,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC;YACjC,MAAM,UAAU,GAAG,KAAK,KAAK,aAAa,CAAC;YAC3C,MAAM,SAAS,GAAG,IAAI,KAAK,UAAU,IAAI,UAAU,CAAC;YAEpD,IAAI,MAAM,GAAG,EAAE,CAAC;YAChB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpB,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YACtC,CAAC;iBAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACxB,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACzC,CAAC;iBAAM,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACtB,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACjC,CAAC;iBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,UAAU,CAAC,CAAC;YAC1D,CAAC;YAED,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YACvC,MAAM,IAAI,GAAG,UAAU,IAAI,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAE9E,OAAO,CACL,MAAC,IAAI,IAAgB,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,aACvD,MAAM,EAAE,IAAI,EAAE,MAAM,KADZ,QAAQ,CAEZ,CACR,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,GAAG,EAAE;QACxB,IAAI,IAAI,KAAK,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAEzC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,kCAAyB,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,OAAO,MAAC,IAAI,IAAC,KAAK,EAAC,KAAK,wBAAS,IAAI,CAAC,KAAK,IAAQ,CAAC;QACtD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,oCAA2B,CAAC;QACvD,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YACtC,MAAM,UAAU,GAAG,KAAK,KAAK,UAAU,CAAC;YACxC,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YAExC,OAAO,CACL,MAAC,IAAI,IAAa,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,aACrD,MAAM,EAAE,KAAK,KADL,KAAK,CAET,CACR,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aAEpC,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,YACpD,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAC,MAAM,2CAAkC,GACrD,EAGN,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,YAClB,MAAC,IAAI,4BACM,MAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAC,OAAO,aAAE,eAAe,OAAG,YAAY,IAAQ,IACpE,GACH,EAGN,MAAC,GAAG,IAAC,aAAa,EAAC,KAAK,aAEtB,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAC,KAAK,EAAC,YAAY,EAAE,CAAC,aACrD,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,2BAEhD,EACP,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,SAAS,EAAE,CAAC,YACrC,eAAe,EAAE,GACd,IACF,EAGN,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAC,KAAK,aACrC,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,wBAE7C,EACP,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,SAAS,EAAE,CAAC,YACrC,YAAY,EAAE,GACX,IACF,IACF,EAGN,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YACjD,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,uGAEX,EACN,IAAI,KAAK,UAAU,IAAI,CACtB,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,0DAEb,CACR,EACA,IAAI,KAAK,OAAO,IAAI,CACnB,MAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,+BACH,gBAAgB,OAAG,aAAa,IAC1C,CACR,IACG,GACF,IACF,CACP,CAAC;AACJ,CAAC"}