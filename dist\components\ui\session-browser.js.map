{"version": 3, "file": "session-browser.js", "sourceRoot": "", "sources": ["../../../src/components/ui/session-browser.tsx"], "names": [], "mappings": ";AAAA;;;;;GAKG;AAEH,OAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACnD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAE1C,OAAO,EACL,YAAY,EAEZ,aAAa,EACb,eAAe,EACf,cAAc,EACf,MAAM,qCAAqC,CAAC;AAQ7C,MAAM,UAAU,cAAc,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAuB;IACtE,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAoB,EAAE,CAAC,CAAC;IAChE,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAS,CAAC,CAAC,CAAC;IAC9D,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,QAAQ,CAA8B,QAAQ,CAAC,CAAC;IACxE,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAM,IAAI,CAAC,CAAC;IAC9C,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAU,IAAI,CAAC,CAAC;IAEtD,yBAAyB;IACzB,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC;gBACH,UAAU,CAAC,IAAI,CAAC,CAAC;gBACjB,MAAM,WAAW,GAAG,YAAY,EAAE,CAAC;gBACnC,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;gBAEvC,WAAW,CAAC,WAAW,CAAC,CAAC;gBACzB,QAAQ,CAAC,YAAY,CAAC,CAAC;YACzB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACnD,CAAC;oBAAS,CAAC;gBACT,UAAU,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACH,CAAC,CAAC;QAEF,YAAY,EAAE,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,wBAAwB;IACxB,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,IAAI,KAAK,gBAAgB,EAAE,CAAC;gBAC9B,OAAO,CAAC,QAAQ,CAAC,CAAC;YACpB,CAAC;iBAAM,CAAC;gBACN,QAAQ,EAAE,CAAC;YACb,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,CAAC,eAAe;gBAAE,OAAO;YAE7B,IAAI,IAAI,KAAK,gBAAgB,EAAE,CAAC;gBAC9B,mBAAmB;gBACnB,MAAM,OAAO,GAAG,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;gBAClD,IAAI,OAAO,EAAE,CAAC;oBACZ,mBAAmB;oBACnB,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,KAAK,aAAa,CAAC,CAAC,CAAC;oBACxE,wBAAwB;oBACxB,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;gBAChE,CAAC;gBACD,OAAO,CAAC,QAAQ,CAAC,CAAC;YACpB,CAAC;iBAAM,CAAC;gBACN,eAAe;gBACf,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;YAC7B,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YAChD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;YAClB,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YAClE,OAAO;QACT,CAAC;QAED,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YACvC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAC1B,OAAO;QACT,CAAC;QAED,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YACvC,mBAAmB;YACnB,MAAM,WAAW,GAAG,YAAY,EAAE,CAAC;YACnC,WAAW,CAAC,WAAW,CAAC,CAAC;YACzB,OAAO;QACT,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,eAAe,GAAG,CAAC,SAAiB,EAAU,EAAE;QACpD,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QACjC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAE5D,IAAI,SAAS,GAAG,CAAC;YAAE,OAAO,kBAAkB,CAAC;QAC7C,IAAI,SAAS,GAAG,EAAE;YAAE,OAAO,GAAG,SAAS,OAAO,CAAC;QAC/C,IAAI,QAAQ,GAAG,CAAC;YAAE,OAAO,GAAG,QAAQ,OAAO,CAAC;QAE5C,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACnC,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,CAAC,QAAgB,EAAU,EAAE;QACnD,MAAM,KAAK,GAA2B;YACpC,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,IAAI;YACjB,QAAQ,EAAE,IAAI;YACd,SAAS,EAAE,KAAK;YAChB,UAAU,EAAE,IAAI;YAChB,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,GAAG;YACX,QAAQ,EAAE,IAAI;SACf,CAAC;QACF,OAAO,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,IAAI,IAAI,CAAC;IAC/C,CAAC,CAAC;IAEF,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,CACL,KAAC,GAAG,IAAC,cAAc,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,MAAM,EAAE,EAAE,YACzD,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,oCAA2B,GAC3C,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aAEpC,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,YACpD,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAC,MAAM,gCAAuB,GAC1C,EAGL,KAAK,IAAI,CACR,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,YAClB,MAAC,IAAI,mCACa,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,YAAE,KAAK,CAAC,aAAa,GAAQ,EAC9D,KAAK,CAAC,SAAS,GAAG,CAAC,IAAI,CACtB,8BACG,KAAK,YACA,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,YAAE,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC,GAAQ,IAClE,CACJ,IACI,GACH,CACP,EAGD,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAE,EAAE,YACnC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CACvB,KAAC,GAAG,IAAC,cAAc,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,YAC5D,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,wCAA+B,GAC7C,CACP,CAAC,CAAC,CAAC,CACF,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oBAC3C,MAAM,UAAU,GAAG,KAAK,KAAK,aAAa,CAAC;oBAC3C,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACvD,MAAM,SAAS,GAAG,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;oBAErD,OAAO,CACL,MAAC,GAAG,IAAkB,aAAa,EAAC,QAAQ,aAC1C,KAAC,GAAG,cACF,MAAC,IAAI,IAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,aACzC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACxB,YAAY,EACZ,GAAG,EACJ,MAAC,IAAI,IAAC,IAAI,EAAE,UAAU,aACnB,OAAO,CAAC,QAAQ,OAAG,OAAO,CAAC,KAAK,IAC5B,EACP,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,yBAAK,OAAO,CAAC,SAAS,cAAc,IACjD,GACH,EACL,UAAU,IAAI,CACb,MAAC,GAAG,IAAC,UAAU,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,aACxC,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,qBACX,OAAO,CAAC,EAAE,IACV,EACP,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,0BACN,SAAS,IACd,EACP,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,gCACA,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC,IAChD,IACH,CACP,KAxBO,OAAO,CAAC,EAAE,CAyBd,CACP,CAAC;gBACJ,CAAC,CAAC,CACH,GACG,EAGL,QAAQ,CAAC,MAAM,GAAG,EAAE,IAAI,CACvB,KAAC,GAAG,IAAC,cAAc,EAAC,QAAQ,EAAC,SAAS,EAAE,CAAC,YACvC,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,yBACP,QAAQ,CAAC,MAAM,GAAG,EAAE,sBACxB,GACH,CACP,EAGA,QAAQ,CAAC,aAAa,CAAC,IAAI,CAC1B,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YACjD,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAC,MAAM,kCAAyB,EAChD,MAAC,IAAI,eACH,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,YAAE,QAAQ,CAAC,aAAa,CAAC,CAAC,QAAQ,GAAQ,OAE9D,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,YAAE,QAAQ,CAAC,aAAa,CAAC,CAAC,KAAK,GAAQ,EACzD,KAAK,EACN,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,aAAE,QAAQ,CAAC,aAAa,CAAC,CAAC,SAAS,cAAc,IAC9D,IACH,GACF,CACP,EAGA,IAAI,KAAK,gBAAgB,IAAI,QAAQ,CAAC,aAAa,CAAC,IAAI,CACvD,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YACjD,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAC,KAAK,8CAA2B,EACjD,MAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,iCACD,QAAQ,CAAC,aAAa,CAAC,CAAC,EAAE,SACtC,EACP,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,8CAEX,IACH,GACF,CACP,EAGD,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YACjD,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,YACxB,IAAI,KAAK,gBAAgB,CAAC,CAAC,CAAC,CAC3B,4BACE,KAAC,IAAI,IAAC,KAAK,EAAC,KAAK,yDAEV,GACN,CACJ,CAAC,CAAC,CAAC,CACF,8BACE,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,4GAEX,EACN,QAAQ,CAAC,aAAa,CAAC,IAAI,CAC1B,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,8CACc,QAAQ,CAAC,aAAa,CAAC,CAAC,EAAE,IACnD,CACR,IACA,CACJ,GACG,GACF,IACF,CACP,CAAC;AACJ,CAAC"}