/**
 * Core Types and Interfaces
 *
 * Defines the fundamental types used throughout the Kritrima AI CLI system
 */
export interface ProviderConfig {
    name: string;
    baseURL: string;
    envKey: string;
}
export type ProviderName = 'openai' | 'azure' | 'gemini' | 'ollama' | 'mistral' | 'deepseek' | 'xai' | 'groq' | 'arceeai' | 'openrouter';
export interface ModelInfo {
    id: string;
    name: string;
    contextLength: number;
    supportsImages: boolean;
    supportsTools: boolean;
    provider: ProviderName;
}
export type ApprovalPolicy = 'suggest' | 'auto-edit' | 'full-auto';
export interface AppConfig {
    model: string;
    provider: ProviderName;
    approvalMode: ApprovalPolicy;
    providers?: Record<string, ProviderConfig>;
    safeCommands?: string[];
    dangerousCommands?: string[];
    maxTokens?: number;
    temperature?: number;
    timeout?: number;
    enableNotifications?: boolean;
    enableLogging?: boolean;
    projectDocPath?: string;
    additionalWritableRoots?: string[];
}
export interface ResponseContentInput {
    type: 'input_text' | 'input_image';
    text?: string;
    image?: {
        url: string;
        detail?: 'low' | 'high' | 'auto';
    };
}
export interface ResponseInputItem {
    role: 'user' | 'assistant' | 'system';
    content: ResponseContentInput[];
    type: 'message';
    timestamp?: number;
}
export interface ResponseOutputItem {
    role: 'assistant';
    content: string;
    type: 'output';
    timestamp?: number;
    metadata?: {
        model: string;
        provider: string;
        tokens?: number;
        thinkingTime?: number;
    };
}
export interface ResponseFunctionToolCall {
    id: string;
    name: string;
    arguments: string;
    type: 'function_call';
    timestamp?: number;
}
export interface ResponseToolResult {
    id: string;
    result: string;
    success: boolean;
    type: 'tool_result';
    timestamp?: number;
    metadata?: {
        command?: string[];
        workdir?: string;
        exitCode?: number;
        duration?: number;
    };
}
export type ResponseItem = ResponseInputItem | ResponseOutputItem | ResponseFunctionToolCall | ResponseToolResult;
export interface FunctionTool {
    type: 'function';
    function: {
        name: string;
        description: string;
        parameters: {
            type: 'object';
            properties: Record<string, any>;
            required: string[];
        };
    };
}
export interface ExecInput {
    command: string[];
    workdir?: string;
    timeout?: number;
}
export interface ExecResult {
    success: boolean;
    output: string;
    error?: string;
    exitCode: number;
    duration: number;
    command: string[];
    workdir: string;
}
export type OverlayModeType = 'none' | 'history' | 'sessions' | 'model' | 'approval' | 'help' | 'diff';
export interface ConfirmationResult {
    decision: 'yes' | 'no_continue' | 'no_exit' | 'always' | 'explain';
    customMessage?: string;
}
export interface HistoryEntry {
    command: string;
    timestamp: number;
    success?: boolean;
}
export interface SessionMetadata {
    id: string;
    timestamp: number;
    model: string;
    provider: string;
    itemCount: number;
    lastActivity: number;
}
export interface SessionData {
    metadata: SessionMetadata;
    items: ResponseItem[];
    config: Partial<AppConfig>;
}
export declare class KritrimaError extends Error {
    code: string;
    details?: any | undefined;
    constructor(message: string, code: string, details?: any | undefined);
}
export declare class NetworkError extends KritrimaError {
    constructor(message: string, details?: any);
}
export declare class ConfigError extends KritrimaError {
    constructor(message: string, details?: any);
}
export declare class SecurityError extends KritrimaError {
    constructor(message: string, details?: any);
}
//# sourceMappingURL=index.d.ts.map