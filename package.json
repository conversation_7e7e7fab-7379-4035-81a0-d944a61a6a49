{"name": "kritrima-ag", "version": "1.0.0", "description": "Sophisticated AI-powered CLI assistant with multi-provider support, autonomous agent loop, and advanced code assistance", "main": "dist/cli.js", "type": "module", "bin": {"kritrima-ai": "./bin/kritrima-ai.js"}, "scripts": {"build": "tsc", "dev": "tsx src/cli.tsx", "start": "node dist/cli.js", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint src/**/*.{ts,tsx}", "format": "prettier --write src/**/*.{ts,tsx}"}, "keywords": ["ai", "cli", "assistant", "openai", "gpt", "coding", "automation", "terminal"], "author": "Kritrima AI", "license": "MIT", "dependencies": {"openai": "^4.67.3", "blessed": "^0.1.81", "ink": "^5.0.1", "react": "^18.3.1", "commander": "^12.1.0", "chalk": "^5.3.0", "ora": "^8.1.0", "inquirer": "^12.0.0", "fs-extra": "^11.2.0", "yaml": "^2.6.0", "zod": "^3.23.8", "node-notifier": "^10.0.1", "https-proxy-agent": "^7.0.5", "semver": "^7.6.3", "diff": "^7.0.0", "highlight.js": "^11.10.0", "marked": "^14.1.2", "glob": "^11.0.0", "chokidar": "^4.0.1", "dotenv": "^16.4.5"}, "devDependencies": {"@types/node": "^22.7.4", "@types/react": "^18.3.11", "@types/blessed": "^0.1.25", "@types/fs-extra": "^11.0.4", "@types/diff": "^5.2.3", "@types/semver": "^7.5.8", "typescript": "^5.6.2", "tsx": "^4.19.1", "vitest": "^2.1.1", "@vitest/ui": "^2.1.1", "eslint": "^9.11.1", "@typescript-eslint/eslint-plugin": "^8.7.0", "@typescript-eslint/parser": "^8.7.0", "prettier": "^3.3.3"}, "engines": {"node": ">=18.0.0"}}