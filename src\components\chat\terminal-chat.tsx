/**
 * Terminal Chat Interface
 * 
 * Main chat component providing real-time AI interaction
 * Handles model switching, approval workflows, and conversation management
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput, useApp } from 'ink';
import chalk from 'chalk';
import { AgentLoop } from '../../utils/agent/agent-loop.js';
import { createInputItem } from '../../utils/input-utils.js';
import { executeSlashCommand, isSlashCommand } from '../../utils/slash-commands.js';
import { expandFileTags } from '../../utils/file-tag-utils.js';
import { addToHistory } from '../../utils/storage/command-history.js';
import { saveRollout } from '../../utils/storage/save-rollout.js';
import { logInfo, logError } from '../../utils/logger/log.js';
import { processMultiModalInput } from '../../utils/multimodal-input.js';
import { TerminalChatInput } from './terminal-chat-input.js';
import { ModelOverlay } from '../overlays/model-overlay.js';
import { HistoryOverlay } from '../overlays/history-overlay.js';
import { SessionsOverlay } from '../overlays/sessions-overlay.js';
import { ApprovalOverlay } from '../overlays/approval-overlay.js';
import { HelpOverlay } from '../overlays/help-overlay.js';
import { Spinner, LoadingSpinner } from '../vendor/spinner.js';
import { useNotifications, NotificationManager } from '../vendor/notification.js';
import { useAgentState } from '../../hooks/use-agent-state.js';
import type { AppConfig, ResponseItem, ApprovalPolicy, OverlayModeType, ProviderName, SessionData } from '../../types/index.js';

interface TerminalChatProps {
  config: AppConfig;
  onExit: () => void;
}

export function TerminalChat({ config, onExit }: TerminalChatProps) {
  const { exit } = useApp();
  
  // State management
  const [model, setModel] = useState<string>(config.model);
  const [provider, setProvider] = useState<string>(config.provider);
  const [approvalPolicy, setApprovalPolicy] = useState<ApprovalPolicy>(config.approvalMode);
  const [items, setItems] = useState<ResponseItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [input, setInput] = useState<string>('');
  const [sessionId] = useState<string>(() => generateSessionId());
  const [overlayMode, setOverlayMode] = useState<OverlayModeType>('none');

  // Advanced state management
  const agentState = useAgentState({
    onStateChange: (state) => {
      setLoading(state !== 'idle');
    },
    onError: (error) => {
      notifications.showError('Agent Error', error.message);
    },
    onComplete: (result) => {
      notifications.showSuccess('Task Completed', 'Agent operation completed successfully');
    }
  });

  const notifications = useNotifications();
  
  // Agent loop instance
  const [agentLoop] = useState(() => new AgentLoop({
    model,
    provider,
    approvalPolicy,
    additionalWritableRoots: config.additionalWritableRoots || []
  }));

  // Update agent loop when config changes
  useEffect(() => {
    agentLoop.updateConfig({
      model,
      provider,
      approvalPolicy
    });
  }, [model, provider, approvalPolicy, agentLoop]);

  // Save session periodically
  useEffect(() => {
    if (items.length > 0) {
      saveRollout(sessionId, items, { model, provider, approvalMode: approvalPolicy });
    }
  }, [items, sessionId, model, provider, approvalPolicy]);

  // Handle keyboard input
  useInput((input, key) => {
    // Global shortcuts
    if (key.ctrl && input === 'c') {
      onExit();
      return;
    }

    // Overlay shortcuts
    if (overlayMode === 'none') {
      if (key.ctrl && input === 'm') {
        setOverlayMode('model');
        return;
      }
      if (key.ctrl && input === 'h') {
        setOverlayMode('history');
        return;
      }
      if (key.ctrl && input === 's') {
        setOverlayMode('sessions');
        return;
      }
      if (key.ctrl && input === 'a') {
        setOverlayMode('approval');
        return;
      }
      if (key.ctrl && input === '?') {
        setOverlayMode('help');
        return;
      }
    }

    // Close overlay
    if (overlayMode !== 'none' && key.escape) {
      setOverlayMode('none');
      return;
    }

    // Don't handle input when overlay is open
    if (overlayMode !== 'none') {
      return;
    }

    if (key.return) {
      handleSubmit();
      return;
    }

    if (key.backspace || key.delete) {
      setInput(prev => prev.slice(0, -1));
      return;
    }

    if (input && !key.ctrl && !key.meta) {
      setInput(prev => prev + input);
    }
  });

  /**
   * Handle message submission
   */
  const handleSubmit = useCallback(async () => {
    if (!input.trim() || loading) {
      return;
    }

    const userInput = input.trim();
    setInput('');
    setLoading(true);

    try {
      logInfo(`Processing user input: ${userInput}`);

      // Add to command history
      addToHistory(userInput);

      // Check for slash commands
      if (isSlashCommand(userInput)) {
        const result = await executeSlashCommand(userInput);
        
        // Handle slash command results
        if (result.command === '/exit' || result.command === '/quit') {
          onExit();
          return;
        }

        // Add slash command result to conversation
        const commandResult: ResponseItem = {
          role: 'assistant',
          content: result.result,
          type: 'output',
          timestamp: Date.now(),
          metadata: {
            model: 'system',
            provider: 'system'
          }
        };

        setItems(prev => [...prev, commandResult]);
        setLoading(false);
        return;
      }

      // Expand file tags
      const expandedInput = await expandFileTags(userInput, process.cwd());

      // Create input item
      const inputItem = await createInputItem(expandedInput, []);

      // Execute agent loop
      const results = await agentLoop.executeLoop(
        inputItem,
        {
          onDelta: (delta) => {
            // Handle streaming text updates
            console.log(chalk.gray(delta));
          },
          onComplete: (content) => {
            logInfo(`AI response completed - Length: ${content.length}`);
          },
          onError: (error) => {
            logError('AI response error', new Error(error));
          },
          onToolCall: (toolCall) => {
            logInfo(`Tool call initiated: ${toolCall.name}`);
          },
          onToolResult: (result) => {
            logInfo(`Tool execution completed - Success: ${result.success}, Command: ${result.metadata?.command?.join(' ') || 'unknown'}`);
          },
          getCommandConfirmation: async (command, workdir) => {
            return await handleCommandApproval(command, workdir);
          }
        }
      );

      // Update conversation items
      setItems(prev => [...prev, ...results]);

      // Mark command as successful
      addToHistory(userInput, true);

    } catch (error) {
      logError('Error processing user input', error instanceof Error ? error : new Error(String(error)));
      
      // Add error message to conversation
      const errorItem: ResponseItem = {
        role: 'assistant',
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        type: 'output',
        timestamp: Date.now(),
        metadata: {
          model: 'system',
          provider: 'system'
        }
      };

      setItems(prev => [...prev, errorItem]);

      // Mark command as failed
      addToHistory(userInput, false);
    } finally {
      setLoading(false);
    }
  }, [input, loading, agentLoop, onExit]);

  /**
   * Handle command approval
   */
  const handleCommandApproval = useCallback(async (
    command: string[],
    workdir: string
  ): Promise<boolean> => {
    // For now, implement simple approval based on policy
    if (approvalPolicy === 'full-auto') {
      return true;
    }

    if (approvalPolicy === 'auto-edit') {
      // Auto-approve safe commands
      const safeCommands = config.safeCommands || [];
      const commandName = command[0]?.toLowerCase();
      return safeCommands.includes(commandName);
    }

    // For 'suggest' mode, always require approval
    // TODO: Implement interactive approval UI
    console.log(chalk.yellow(`\nCommand approval required:`));
    console.log(chalk.gray(`Command: ${command.join(' ')}`));
    console.log(chalk.gray(`Working directory: ${workdir}`));
    console.log(chalk.blue('Auto-approving for demo purposes...'));
    
    return true; // Auto-approve for now
  }, [approvalPolicy, config.safeCommands]);

  /**
   * Render conversation items
   */
  const renderItems = () => {
    return items.map((item, index) => {
      if (item.type === 'message') {
        return (
          <Box key={index} flexDirection="column" marginBottom={1}>
            <Text color="blue" bold>
              {item.role === 'user' ? '👤 You:' : '🤖 Assistant:'}
            </Text>
            {item.content.map((content, contentIndex) => (
              <Text key={contentIndex}>
                {content.type === 'input_text' ? content.text : '[Image]'}
              </Text>
            ))}
          </Box>
        );
      }

      if (item.type === 'output') {
        return (
          <Box key={index} flexDirection="column" marginBottom={1}>
            <Text color="green" bold>🤖 Assistant:</Text>
            <Text>{item.content}</Text>
          </Box>
        );
      }

      if (item.type === 'function_call') {
        return (
          <Box key={index} flexDirection="column" marginBottom={1}>
            <Text color="yellow" bold>🔧 Tool Call:</Text>
            <Text color="gray">{item.name}({item.arguments})</Text>
          </Box>
        );
      }

      if (item.type === 'tool_result') {
        return (
          <Box key={index} flexDirection="column" marginBottom={1}>
            <Text color={item.success ? "green" : "red"} bold>
              {item.success ? '✅' : '❌'} Tool Result:
            </Text>
            <Text>{item.result}</Text>
          </Box>
        );
      }

      return null;
    });
  };

  return (
    <Box flexDirection="column" height="100%">
      {/* Header */}
      <Box borderStyle="single" paddingX={1}>
        <Text color="blue" bold>
          Kritrima AI CLI - {provider}/{model} ({approvalPolicy})
        </Text>
      </Box>

      {/* Conversation area */}
      <Box flexDirection="column" flexGrow={1} paddingX={1} paddingY={1}>
        {renderItems()}
        
        {loading && (
          <Box>
            <Text color="yellow">🤔 Thinking...</Text>
          </Box>
        )}
      </Box>

      {/* Input area */}
      <Box borderStyle="single" paddingX={1}>
        <Text color="cyan">💬 </Text>
        <Text>{input}</Text>
        <Text color="gray">█</Text>
      </Box>

      {/* Status bar */}
      <Box paddingX={1}>
        <Text color="gray" dimColor>
          Press Ctrl+C to exit • Type /help for commands • {items.length} messages
        </Text>
      </Box>
    </Box>
  );
}

/**
 * Generate unique session ID
 */
function generateSessionId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}
