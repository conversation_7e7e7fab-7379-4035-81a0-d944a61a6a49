/**
 * Help Information Overlay
 * 
 * Comprehensive help system with keyboard shortcuts, commands, and usage tips
 * Provides contextual help and feature explanations
 */

import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { SLASH_COMMANDS } from '../../utils/slash-commands.js';
import { CLI_VERSION } from '../../version.js';

interface HelpOverlayProps {
  onClose: () => void;
  visible: boolean;
}

type HelpSection = 'overview' | 'commands' | 'shortcuts' | 'features' | 'tips';

interface HelpSectionInfo {
  id: HelpSection;
  title: string;
  description: string;
}

const HELP_SECTIONS: HelpSectionInfo[] = [
  {
    id: 'overview',
    title: 'Overview',
    description: 'Introduction and basic usage'
  },
  {
    id: 'commands',
    title: 'Slash Commands',
    description: 'Built-in commands and their usage'
  },
  {
    id: 'shortcuts',
    title: 'Keyboard Shortcuts',
    description: 'Navigation and productivity shortcuts'
  },
  {
    id: 'features',
    title: 'Features',
    description: 'Advanced features and capabilities'
  },
  {
    id: 'tips',
    title: 'Tips & Tricks',
    description: 'Best practices and productivity tips'
  }
];

export function HelpOverlay({ onClose, visible }: HelpOverlayProps) {
  const [selectedSection, setSelectedSection] = useState<HelpSection>('overview');

  // Handle keyboard input
  useInput((input, key) => {
    if (!visible) return;

    // Close overlay
    if (key.escape) {
      onClose();
      return;
    }

    // Section navigation
    if (key.tab) {
      const currentIndex = HELP_SECTIONS.findIndex(s => s.id === selectedSection);
      const nextIndex = (currentIndex + 1) % HELP_SECTIONS.length;
      setSelectedSection(HELP_SECTIONS[nextIndex].id);
      return;
    }

    // Quick section selection
    if (input >= '1' && input <= '5') {
      const index = parseInt(input) - 1;
      if (index >= 0 && index < HELP_SECTIONS.length) {
        setSelectedSection(HELP_SECTIONS[index].id);
      }
      return;
    }
  });

  /**
   * Render overview section
   */
  const renderOverview = () => (
    <Box flexDirection="column">
      <Text color="blue" bold marginBottom={1}>
        Welcome to Kritrima AI CLI v{CLI_VERSION}
      </Text>
      
      <Text marginBottom={1}>
        A sophisticated AI-powered CLI assistant with multi-provider support,
        autonomous agent capabilities, and advanced code assistance.
      </Text>

      <Text color="blue" bold marginTop={1} marginBottom={1}>
        Getting Started:
      </Text>
      
      <Text>• Type your questions or requests in natural language</Text>
      <Text>• Use @filename to include files in your context</Text>
      <Text>• Start commands with / for built-in functions</Text>
      <Text>• Press Tab for auto-completion</Text>
      <Text>• Use arrow keys to navigate command history</Text>

      <Text color="blue" bold marginTop={2} marginBottom={1}>
        Key Features:
      </Text>
      
      <Text>• Multi-provider AI support (OpenAI, Gemini, Ollama, etc.)</Text>
      <Text>• Autonomous tool calling and command execution</Text>
      <Text>• Advanced file operations and code assistance</Text>
      <Text>• Session management and conversation history</Text>
      <Text>• Configurable approval modes for security</Text>
      <Text>• Real-time streaming responses</Text>
    </Box>
  );

  /**
   * Render commands section
   */
  const renderCommands = () => (
    <Box flexDirection="column">
      <Text color="blue" bold marginBottom={1}>
        Available Slash Commands:
      </Text>
      
      {SLASH_COMMANDS.map((cmd, index) => (
        <Box key={cmd.command} marginBottom={1}>
          <Box width={15}>
            <Text color="cyan" bold>
              {cmd.command}
            </Text>
          </Box>
          <Box flexGrow={1}>
            <Text>{cmd.description}</Text>
          </Box>
        </Box>
      ))}

      <Text color="blue" bold marginTop={2} marginBottom={1}>
        Command Usage:
      </Text>
      
      <Text>• Commands start with / (e.g., /help, /model)</Text>
      <Text>• Some commands accept parameters (e.g., /model set gpt-4)</Text>
      <Text>• Use Tab for command auto-completion</Text>
      <Text>• Type /help &lt;command&gt; for detailed help</Text>
    </Box>
  );

  /**
   * Render shortcuts section
   */
  const renderShortcuts = () => (
    <Box flexDirection="column">
      <Text color="blue" bold marginBottom={1}>
        Keyboard Shortcuts:
      </Text>

      <Text color="yellow" bold marginTop={1}>Navigation:</Text>
      <Text>• ↑/↓ Arrow Keys    - Navigate command history</Text>
      <Text>• Tab              - Auto-complete commands/files</Text>
      <Text>• Escape           - Close overlays/clear input</Text>
      <Text>• Page Up/Down     - Scroll through lists</Text>
      <Text>• Home/End         - Jump to start/end of lists</Text>

      <Text color="yellow" bold marginTop={2}>Input & Editing:</Text>
      <Text>• Enter            - Submit command/message</Text>
      <Text>• Shift+Enter      - New line (multiline mode)</Text>
      <Text>• Ctrl+A           - Select all text</Text>
      <Text>• Ctrl+C           - Copy selected text</Text>
      <Text>• Ctrl+V           - Paste text</Text>
      <Text>• Ctrl+Z           - Undo</Text>
      <Text>• Ctrl+Y           - Redo</Text>

      <Text color="yellow" bold marginTop={2}>Application:</Text>
      <Text>• Ctrl+C           - Exit application</Text>
      <Text>• Ctrl+F           - Toggle search mode</Text>
      <Text>• Ctrl+L           - Clear screen</Text>

      <Text color="yellow" bold marginTop={2}>Overlays:</Text>
      <Text>• /model           - Open model selection</Text>
      <Text>• /history         - Open command history</Text>
      <Text>• /sessions        - Open session browser</Text>
      <Text>• /approval        - Open approval settings</Text>
      <Text>• /help            - Open this help</Text>
    </Box>
  );

  /**
   * Render features section
   */
  const renderFeatures = () => (
    <Box flexDirection="column">
      <Text color="blue" bold marginBottom={1}>
        Advanced Features:
      </Text>

      <Text color="yellow" bold marginTop={1}>File Integration:</Text>
      <Text>• Use @filename to include files in context</Text>
      <Text>• Supports relative and absolute paths</Text>
      <Text>• Auto-completion for file paths</Text>
      <Text>• Automatic file content expansion</Text>

      <Text color="yellow" bold marginTop={2}>AI Providers:</Text>
      <Text>• OpenAI (GPT-3.5, GPT-4, GPT-4 Turbo)</Text>
      <Text>• Google Gemini (Gemini Pro, Gemini Ultra)</Text>
      <Text>• Ollama (Local models)</Text>
      <Text>• Mistral AI, DeepSeek, xAI, Groq</Text>
      <Text>• Custom provider support</Text>

      <Text color="yellow" bold marginTop={2}>Security & Approval:</Text>
      <Text>• Suggest Mode: Manual approval for all actions</Text>
      <Text>• Auto-Edit Mode: Automatic file edits, manual commands</Text>
      <Text>• Full-Auto Mode: Automatic everything (sandboxed)</Text>
      <Text>• Configurable safe command lists</Text>

      <Text color="yellow" bold marginTop={2}>Session Management:</Text>
      <Text>• Automatic conversation saving</Text>
      <Text>• Session browsing and restoration</Text>
      <Text>• Export/import capabilities</Text>
      <Text>• Session search and filtering</Text>

      <Text color="yellow" bold marginTop={2}>Tool Calling:</Text>
      <Text>• Autonomous shell command execution</Text>
      <Text>• File operations and code generation</Text>
      <Text>• Git integration and diff viewing</Text>
      <Text>• Package manager detection</Text>
    </Box>
  );

  /**
   * Render tips section
   */
  const renderTips = () => (
    <Box flexDirection="column">
      <Text color="blue" bold marginBottom={1}>
        Tips & Best Practices:
      </Text>

      <Text color="yellow" bold marginTop={1}>Productivity Tips:</Text>
      <Text>• Use descriptive prompts for better AI responses</Text>
      <Text>• Include relevant files with @filename syntax</Text>
      <Text>• Break complex tasks into smaller steps</Text>
      <Text>• Use /compact to reduce token usage in long conversations</Text>
      <Text>• Save important sessions for future reference</Text>

      <Text color="yellow" bold marginTop={2}>Security Best Practices:</Text>
      <Text>• Use 'suggest' mode in production environments</Text>
      <Text>• Review commands before approval</Text>
      <Text>• Avoid including sensitive data in prompts</Text>
      <Text>• Regularly clean up old sessions</Text>
      <Text>• Use environment variables for API keys</Text>

      <Text color="yellow" bold marginTop={2}>Troubleshooting:</Text>
      <Text>• Check API key configuration if models don't load</Text>
      <Text>• Use /bug to generate bug reports</Text>
      <Text>• Enable debug mode with --debug flag</Text>
      <Text>• Check network connectivity for provider issues</Text>
      <Text>• Clear history if experiencing performance issues</Text>

      <Text color="yellow" bold marginTop={2}>Performance Tips:</Text>
      <Text>• Use shorter context for faster responses</Text>
      <Text>• Choose appropriate models for your tasks</Text>
      <Text>• Enable notifications to work on other tasks</Text>
      <Text>• Use local models (Ollama) for privacy</Text>
      <Text>• Regularly update to the latest version</Text>
    </Box>
  );

  /**
   * Render content based on selected section
   */
  const renderContent = () => {
    switch (selectedSection) {
      case 'overview': return renderOverview();
      case 'commands': return renderCommands();
      case 'shortcuts': return renderShortcuts();
      case 'features': return renderFeatures();
      case 'tips': return renderTips();
      default: return renderOverview();
    }
  };

  if (!visible) {
    return null;
  }

  return (
    <Box
      position="absolute"
      top={0}
      left={0}
      right={0}
      bottom={0}
      borderStyle="double"
      borderColor="cyan"
      backgroundColor="black"
      flexDirection="column"
    >
      {/* Header */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor="gray">
        <Text color="cyan" bold>
          Kritrima AI CLI Help System
        </Text>
        <Box marginLeft={2}>
          <Text color="gray">
            Tab: Switch sections • 1-5: Quick select • Esc: Close
          </Text>
        </Box>
      </Box>

      {/* Section tabs */}
      <Box paddingX={2} paddingY={1}>
        {HELP_SECTIONS.map((section, index) => (
          <Box key={section.id} marginRight={2}>
            <Text
              color={selectedSection === section.id ? 'black' : 'gray'}
              backgroundColor={selectedSection === section.id ? 'cyan' : undefined}
              bold={selectedSection === section.id}
            >
              {index + 1}. {section.title}
            </Text>
          </Box>
        ))}
      </Box>

      {/* Content */}
      <Box flexGrow={1} paddingX={2} paddingY={1}>
        {renderContent()}
      </Box>

      {/* Footer */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor="gray">
        <Text color="gray">
          Kritrima AI CLI v{CLI_VERSION} • For more help, visit our documentation
        </Text>
      </Box>
    </Box>
  );
}
