/**
 * Command History Overlay
 * 
 * Browse and search through command history with keyboard navigation
 * Supports filtering, selection, and execution of previous commands
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
import { getHistory, searchHistory, getHistoryStats } from '../../utils/storage/command-history.js';
import type { HistoryEntry } from '../../types/index.js';

interface HistoryOverlayProps {
  onSelectCommand: (command: string) => void;
  onClose: () => void;
  visible: boolean;
}

export function HistoryOverlay({
  onSelectCommand,
  onClose,
  visible
}: HistoryOverlayProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchMode, setSearchMode] = useState(false);
  const [filteredHistory, setFilteredHistory] = useState<HistoryEntry[]>([]);
  const [stats, setStats] = useState<any>(null);

  // Load history and stats
  useEffect(() => {
    if (visible) {
      const history = getHistory();
      const historyStats = getHistoryStats();
      
      setFilteredHistory(history.reverse()); // Show newest first
      setStats(historyStats);
      setSelectedIndex(0);
      setSearchQuery('');
      setSearchMode(false);
    }
  }, [visible]);

  // Update filtered history when search query changes
  useEffect(() => {
    if (searchQuery.trim()) {
      const results = searchHistory(searchQuery);
      setFilteredHistory(results.reverse()); // Show newest first
      setSelectedIndex(0);
    } else {
      const history = getHistory();
      setFilteredHistory(history.reverse());
      setSelectedIndex(0);
    }
  }, [searchQuery]);

  // Handle keyboard input
  useInput((input, key) => {
    if (!visible) return;

    // Close overlay
    if (key.escape) {
      if (searchMode && searchQuery) {
        // Clear search first
        setSearchQuery('');
        setSearchMode(false);
      } else {
        onClose();
      }
      return;
    }

    // Toggle search mode
    if (key.ctrl && input === 'f') {
      setSearchMode(!searchMode);
      return;
    }

    // Handle search mode
    if (searchMode) {
      if (key.backspace || key.delete) {
        setSearchQuery(prev => prev.slice(0, -1));
        return;
      }

      if (key.return) {
        setSearchMode(false);
        return;
      }

      if (input && !key.ctrl && !key.meta) {
        setSearchQuery(prev => prev + input);
        return;
      }
    } else {
      // Navigation mode
      if (key.upArrow) {
        setSelectedIndex(Math.max(0, selectedIndex - 1));
        return;
      }

      if (key.downArrow) {
        setSelectedIndex(Math.min(filteredHistory.length - 1, selectedIndex + 1));
        return;
      }

      if (key.pageUp) {
        setSelectedIndex(Math.max(0, selectedIndex - 10));
        return;
      }

      if (key.pageDown) {
        setSelectedIndex(Math.min(filteredHistory.length - 1, selectedIndex + 10));
        return;
      }

      if (key.home) {
        setSelectedIndex(0);
        return;
      }

      if (key.end) {
        setSelectedIndex(filteredHistory.length - 1);
        return;
      }

      // Select command
      if (key.return) {
        const selectedEntry = filteredHistory[selectedIndex];
        if (selectedEntry) {
          onSelectCommand(selectedEntry.command);
          onClose();
        }
        return;
      }

      // Start search
      if (input === '/') {
        setSearchMode(true);
        return;
      }
    }
  });

  /**
   * Format timestamp for display
   */
  const formatTimestamp = useCallback((timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return date.toLocaleTimeString();
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  }, []);

  /**
   * Get success indicator
   */
  const getSuccessIndicator = useCallback((success?: boolean): string => {
    if (success === true) return '✓';
    if (success === false) return '✗';
    return '•';
  }, []);

  if (!visible) {
    return null;
  }

  return (
    <Box
      position="absolute"
      top={1}
      left={1}
      right={1}
      bottom={1}
      borderStyle="double"
      borderColor="cyan"
      backgroundColor="black"
      flexDirection="column"
    >
      {/* Header */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor="gray">
        <Text color="cyan" bold>
          Command History
        </Text>
        {stats && (
          <Box marginLeft={2}>
            <Text color="gray">
              {stats.totalCommands} total • {stats.uniqueCommands} unique • {stats.successfulCommands} successful
            </Text>
          </Box>
        )}
      </Box>

      {/* Search bar */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor={searchMode ? "yellow" : "gray"}>
        <Text color="blue">Search: </Text>
        <Text color={searchMode ? "yellow" : "white"}>
          {searchQuery || (searchMode ? "█" : "Type / to search, Ctrl+F to toggle")}
        </Text>
        {searchQuery && (
          <Text color="gray" marginLeft={2}>
            ({filteredHistory.length} results)
          </Text>
        )}
      </Box>

      {/* History list */}
      <Box flexGrow={1} paddingX={2} paddingY={1} flexDirection="column">
        {filteredHistory.length === 0 ? (
          <Text color="gray">
            {searchQuery ? 'No commands found matching your search.' : 'No command history available.'}
          </Text>
        ) : (
          <>
            {/* Visible range calculation */}
            {(() => {
              const maxVisible = 15; // Approximate visible items
              const startIndex = Math.max(0, selectedIndex - Math.floor(maxVisible / 2));
              const endIndex = Math.min(filteredHistory.length, startIndex + maxVisible);
              const visibleItems = filteredHistory.slice(startIndex, endIndex);

              return visibleItems.map((entry, index) => {
                const actualIndex = startIndex + index;
                const isSelected = actualIndex === selectedIndex;

                return (
                  <Box key={`${entry.timestamp}-${entry.command}`} marginBottom={1}>
                    <Box width={3}>
                      <Text color={isSelected ? "black" : "gray"} backgroundColor={isSelected ? "cyan" : undefined}>
                        {getSuccessIndicator(entry.success)}
                      </Text>
                    </Box>
                    <Box width={12}>
                      <Text color={isSelected ? "black" : "gray"} backgroundColor={isSelected ? "cyan" : undefined}>
                        {formatTimestamp(entry.timestamp)}
                      </Text>
                    </Box>
                    <Box flexGrow={1}>
                      <Text
                        color={isSelected ? "black" : "white"}
                        backgroundColor={isSelected ? "cyan" : undefined}
                        bold={isSelected}
                      >
                        {entry.command.length > 60 ? `${entry.command.substring(0, 57)}...` : entry.command}
                      </Text>
                    </Box>
                  </Box>
                );
              });
            })()}
          </>
        )}
      </Box>

      {/* Footer */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor="gray">
        <Text color="gray">
          ↑↓: Navigate • Enter: Select • /: Search • Ctrl+F: Toggle search • Esc: Close
        </Text>
        {filteredHistory.length > 0 && (
          <Box marginLeft={2}>
            <Text color="gray">
              {selectedIndex + 1}/{filteredHistory.length}
            </Text>
          </Box>
        )}
      </Box>
    </Box>
  );
}
