/**
 * Approval Mode Selection Component
 * 
 * Interactive interface for selecting command approval policies
 * Provides detailed explanations and security warnings
 */

import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import chalk from 'chalk';
import type { ApprovalPolicy } from '../../types/index.js';

interface ApprovalSelectorProps {
  currentMode: ApprovalPolicy;
  onSelect: (mode: ApprovalPolicy) => void;
  onCancel: () => void;
}

interface ApprovalModeInfo {
  mode: ApprovalPolicy;
  title: string;
  description: string;
  security: 'high' | 'medium' | 'low';
  details: string[];
  examples: string[];
}

const APPROVAL_MODES: ApprovalModeInfo[] = [
  {
    mode: 'suggest',
    title: 'Suggest Mode',
    description: 'AI suggests commands but requires manual approval for execution',
    security: 'high',
    details: [
      '• All commands require explicit user approval',
      '• Shows command details before execution',
      '• Safest option for production environments',
      '• Allows review of potentially dangerous operations'
    ],
    examples: [
      'AI suggests: rm -rf temp/',
      'User sees preview and can approve/deny',
      'Perfect for learning and safety'
    ]
  },
  {
    mode: 'auto-edit',
    title: 'Auto-Edit Mode',
    description: 'Automatically executes safe commands, asks for approval on risky ones',
    security: 'medium',
    details: [
      '• Safe commands execute automatically (ls, cat, grep)',
      '• Risky commands require approval (rm, chmod, sudo)',
      '• Balanced approach for experienced users',
      '• Configurable safe command list'
    ],
    examples: [
      'ls, cat, grep → Auto-execute',
      'rm, sudo, chmod → Ask approval',
      'Good for development workflows'
    ]
  },
  {
    mode: 'full-auto',
    title: 'Full Auto Mode',
    description: 'Executes all AI-suggested commands automatically without approval',
    security: 'low',
    details: [
      '• All commands execute immediately',
      '• Maximum automation and speed',
      '• Requires high trust in AI decisions',
      '• Use only in controlled environments'
    ],
    examples: [
      'All commands execute instantly',
      'No interruptions or confirmations',
      'Use with caution!'
    ]
  }
];

export function ApprovalSelector({ 
  currentMode, 
  onSelect, 
  onCancel 
}: ApprovalSelectorProps) {
  const [selectedIndex, setSelectedIndex] = useState<number>(
    APPROVAL_MODES.findIndex(mode => mode.mode === currentMode)
  );

  // Handle keyboard input
  useInput((input, key) => {
    if (key.escape) {
      onCancel();
      return;
    }

    if (key.return) {
      const selectedMode = APPROVAL_MODES[selectedIndex];
      if (selectedMode) {
        onSelect(selectedMode.mode);
      }
      return;
    }

    if (key.upArrow) {
      setSelectedIndex(prev => Math.max(0, prev - 1));
      return;
    }

    if (key.downArrow) {
      setSelectedIndex(prev => Math.min(APPROVAL_MODES.length - 1, prev + 1));
      return;
    }
  });

  const getSecurityColor = (security: string) => {
    switch (security) {
      case 'high': return 'green';
      case 'medium': return 'yellow';
      case 'low': return 'red';
      default: return 'gray';
    }
  };

  const getSecurityIcon = (security: string) => {
    switch (security) {
      case 'high': return '🛡️';
      case 'medium': return '⚠️';
      case 'low': return '⚡';
      default: return '❓';
    }
  };

  return (
    <Box flexDirection="column" padding={1}>
      {/* Header */}
      <Box borderStyle="single" paddingX={1} marginBottom={1}>
        <Text bold color="blue">Command Approval Mode Selection</Text>
      </Box>

      {/* Current mode */}
      <Box marginBottom={1}>
        <Text>
          Current mode: <Text bold color="green">{currentMode}</Text>
        </Text>
      </Box>

      {/* Mode list */}
      <Box flexDirection="column">
        {APPROVAL_MODES.map((modeInfo, index) => {
          const isSelected = index === selectedIndex;
          const isCurrent = modeInfo.mode === currentMode;
          
          return (
            <Box key={modeInfo.mode} flexDirection="column" marginBottom={1}>
              {/* Mode header */}
              <Box>
                <Text color={isSelected ? 'cyan' : undefined}>
                  {isSelected ? '▶ ' : '  '}
                  <Text bold>{modeInfo.title}</Text>
                  {isCurrent && <Text color="green"> (current)</Text>}
                </Text>
              </Box>

              {/* Security indicator */}
              <Box marginLeft={2}>
                <Text color={getSecurityColor(modeInfo.security)}>
                  {getSecurityIcon(modeInfo.security)} Security: {modeInfo.security.toUpperCase()}
                </Text>
              </Box>

              {/* Description */}
              <Box marginLeft={2} marginTop={1}>
                <Text color="gray">{modeInfo.description}</Text>
              </Box>

              {/* Detailed view for selected mode */}
              {isSelected && (
                <Box flexDirection="column" marginLeft={2} marginTop={1}>
                  {/* Details */}
                  <Box flexDirection="column">
                    <Text bold color="cyan">Details:</Text>
                    {modeInfo.details.map((detail, i) => (
                      <Text key={i} color="gray">{detail}</Text>
                    ))}
                  </Box>

                  {/* Examples */}
                  <Box flexDirection="column" marginTop={1}>
                    <Text bold color="cyan">Examples:</Text>
                    {modeInfo.examples.map((example, i) => (
                      <Text key={i} color="yellow">• {example}</Text>
                    ))}
                  </Box>

                  {/* Security warning for low security modes */}
                  {modeInfo.security === 'low' && (
                    <Box marginTop={1} borderStyle="single" paddingX={1}>
                      <Text color="red" bold>
                        ⚠️ WARNING: This mode executes commands without approval!
                      </Text>
                    </Box>
                  )}
                </Box>
              )}
            </Box>
          );
        })}
      </Box>

      {/* Instructions */}
      <Box borderStyle="single" paddingX={1} marginTop={1}>
        <Box flexDirection="column">
          <Text color="gray">
            ↑/↓ Navigate • Enter Select • Esc Cancel
          </Text>
          <Text color="yellow">
            Choose approval mode: {APPROVAL_MODES[selectedIndex]?.title}
          </Text>
        </Box>
      </Box>

      {/* Security recommendations */}
      <Box flexDirection="column" marginTop={1} borderStyle="single" paddingX={1}>
        <Text bold color="blue">Security Recommendations:</Text>
        <Text color="green">🛡️ Suggest: Best for learning and production</Text>
        <Text color="yellow">⚠️ Auto-Edit: Good for development workflows</Text>
        <Text color="red">⚡ Full-Auto: Use only in controlled environments</Text>
      </Box>
    </Box>
  );
}
