/**
 * Notification System
 * 
 * Provides toast-style notifications with different types and animations
 * Supports auto-dismiss, actions, and persistent notifications
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Text } from 'ink';

export type NotificationType = 'info' | 'success' | 'warning' | 'error';

export interface NotificationData {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number; // in milliseconds, 0 for persistent
  actions?: NotificationAction[];
  timestamp: number;
}

export interface NotificationAction {
  label: string;
  key: string;
  action: () => void;
}

export interface NotificationProps {
  notification: NotificationData;
  onDismiss: (id: string) => void;
  onAction: (id: string, actionKey: string) => void;
}

export interface NotificationManagerProps {
  notifications: NotificationData[];
  maxVisible?: number;
  position?: 'top' | 'bottom';
  onDismiss: (id: string) => void;
  onAction: (id: string, actionKey: string) => void;
}

/**
 * Single notification component
 */
export function Notification({
  notification,
  onDismiss,
  onAction
}: NotificationProps) {
  const [timeLeft, setTimeLeft] = useState(notification.duration || 0);

  useEffect(() => {
    if (notification.duration && notification.duration > 0) {
      const startTime = Date.now();
      const endTime = notification.timestamp + notification.duration;
      
      const timer = setInterval(() => {
        const now = Date.now();
        const remaining = Math.max(0, endTime - now);
        
        setTimeLeft(remaining);
        
        if (remaining === 0) {
          onDismiss(notification.id);
        }
      }, 100);

      return () => clearInterval(timer);
    }
  }, [notification.id, notification.duration, notification.timestamp, onDismiss]);

  const getIcon = (): string => {
    switch (notification.type) {
      case 'success': return '✓';
      case 'error': return '✗';
      case 'warning': return '⚠';
      case 'info': return 'ℹ';
      default: return '•';
    }
  };

  const getColor = (): string => {
    switch (notification.type) {
      case 'success': return 'green';
      case 'error': return 'red';
      case 'warning': return 'yellow';
      case 'info': return 'blue';
      default: return 'white';
    }
  };

  const formatTimeLeft = (ms: number): string => {
    const seconds = Math.ceil(ms / 1000);
    return `${seconds}s`;
  };

  return (
    <Box
      borderStyle="round"
      borderColor={getColor()}
      paddingX={1}
      paddingY={0}
      marginBottom={1}
      flexDirection="column"
    >
      {/* Header */}
      <Box justifyContent="space-between">
        <Box>
          <Text color={getColor()} bold>
            {getIcon()} {notification.title}
          </Text>
        </Box>
        <Box>
          {notification.duration && notification.duration > 0 && timeLeft > 0 && (
            <Text color="gray" dimColor>
              {formatTimeLeft(timeLeft)}
            </Text>
          )}
          <Text color="gray" dimColor marginLeft={1}>
            [ESC]
          </Text>
        </Box>
      </Box>

      {/* Message */}
      {notification.message && (
        <Box marginTop={1}>
          <Text>{notification.message}</Text>
        </Box>
      )}

      {/* Actions */}
      {notification.actions && notification.actions.length > 0 && (
        <Box marginTop={1} gap={1}>
          {notification.actions.map((action, index) => (
            <Text key={action.key} color="cyan">
              [{action.key}] {action.label}
            </Text>
          ))}
        </Box>
      )}
    </Box>
  );
}

/**
 * Notification manager component
 */
export function NotificationManager({
  notifications,
  maxVisible = 5,
  position = 'top',
  onDismiss,
  onAction
}: NotificationManagerProps) {
  // Sort notifications by timestamp (newest first for top, oldest first for bottom)
  const sortedNotifications = [...notifications].sort((a, b) => {
    return position === 'top' ? b.timestamp - a.timestamp : a.timestamp - b.timestamp;
  });

  // Limit visible notifications
  const visibleNotifications = sortedNotifications.slice(0, maxVisible);

  if (visibleNotifications.length === 0) {
    return null;
  }

  return (
    <Box
      position="absolute"
      top={position === 'top' ? 1 : undefined}
      bottom={position === 'bottom' ? 1 : undefined}
      right={1}
      width={60}
      flexDirection="column"
    >
      {visibleNotifications.map(notification => (
        <Notification
          key={notification.id}
          notification={notification}
          onDismiss={onDismiss}
          onAction={onAction}
        />
      ))}
      
      {notifications.length > maxVisible && (
        <Box paddingX={1}>
          <Text color="gray" dimColor>
            +{notifications.length - maxVisible} more notifications
          </Text>
        </Box>
      )}
    </Box>
  );
}

/**
 * Notification hook for managing notifications
 */
export function useNotifications() {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);

  const addNotification = useCallback((
    type: NotificationType,
    title: string,
    message?: string,
    options?: {
      duration?: number;
      actions?: NotificationAction[];
      id?: string;
    }
  ) => {
    const notification: NotificationData = {
      id: options?.id || `notification-${Date.now()}-${Math.random()}`,
      type,
      title,
      message,
      duration: options?.duration ?? (type === 'error' ? 0 : 5000), // Errors persist by default
      actions: options?.actions,
      timestamp: Date.now()
    };

    setNotifications(prev => [...prev, notification]);
    return notification.id;
  }, []);

  const dismissNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const handleAction = useCallback((id: string, actionKey: string) => {
    const notification = notifications.find(n => n.id === id);
    if (notification) {
      const action = notification.actions?.find(a => a.key === actionKey);
      if (action) {
        action.action();
        // Optionally dismiss notification after action
        dismissNotification(id);
      }
    }
  }, [notifications, dismissNotification]);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  // Convenience methods
  const showInfo = useCallback((title: string, message?: string, options?: any) => {
    return addNotification('info', title, message, options);
  }, [addNotification]);

  const showSuccess = useCallback((title: string, message?: string, options?: any) => {
    return addNotification('success', title, message, options);
  }, [addNotification]);

  const showWarning = useCallback((title: string, message?: string, options?: any) => {
    return addNotification('warning', title, message, options);
  }, [addNotification]);

  const showError = useCallback((title: string, message?: string, options?: any) => {
    return addNotification('error', title, message, options);
  }, [addNotification]);

  return {
    notifications,
    addNotification,
    dismissNotification,
    handleAction,
    clearAll,
    showInfo,
    showSuccess,
    showWarning,
    showError
  };
}

/**
 * Simple toast notification
 */
export interface ToastProps {
  type: NotificationType;
  message: string;
  visible: boolean;
  onDismiss?: () => void;
}

export function Toast({ type, message, visible, onDismiss }: ToastProps) {
  useEffect(() => {
    if (visible && onDismiss) {
      const timer = setTimeout(onDismiss, 3000);
      return () => clearTimeout(timer);
    }
  }, [visible, onDismiss]);

  if (!visible) {
    return null;
  }

  const getIcon = (): string => {
    switch (type) {
      case 'success': return '✓';
      case 'error': return '✗';
      case 'warning': return '⚠';
      case 'info': return 'ℹ';
      default: return '•';
    }
  };

  const getColor = (): string => {
    switch (type) {
      case 'success': return 'green';
      case 'error': return 'red';
      case 'warning': return 'yellow';
      case 'info': return 'blue';
      default: return 'white';
    }
  };

  return (
    <Box
      position="absolute"
      top={1}
      right={1}
      borderStyle="round"
      borderColor={getColor()}
      paddingX={2}
      paddingY={1}
      backgroundColor="black"
    >
      <Text color={getColor()}>
        {getIcon()} {message}
      </Text>
    </Box>
  );
}

/**
 * Progress notification with percentage
 */
export interface ProgressNotificationProps {
  title: string;
  progress: number; // 0-100
  message?: string;
  visible: boolean;
}

export function ProgressNotification({
  title,
  progress,
  message,
  visible
}: ProgressNotificationProps) {
  if (!visible) {
    return null;
  }

  const progressBar = '█'.repeat(Math.floor(progress / 5)) + 
                     '░'.repeat(20 - Math.floor(progress / 5));

  return (
    <Box
      position="absolute"
      top={1}
      right={1}
      borderStyle="round"
      borderColor="blue"
      paddingX={2}
      paddingY={1}
      backgroundColor="black"
      flexDirection="column"
    >
      <Text color="blue" bold>
        {title}
      </Text>
      <Box marginTop={1}>
        <Text color="cyan">
          [{progressBar}] {Math.round(progress)}%
        </Text>
      </Box>
      {message && (
        <Box marginTop={1}>
          <Text color="gray">{message}</Text>
        </Box>
      )}
    </Box>
  );
}
