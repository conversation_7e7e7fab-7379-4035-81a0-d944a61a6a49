/**
 * Advanced Spinner Component
 * 
 * Provides various spinner animations with customizable styles and text
 * Supports different spinner types and loading states
 */

import React, { useState, useEffect } from 'react';
import { Text } from 'ink';

export type SpinnerType = 
  | 'dots' 
  | 'line' 
  | 'pipe' 
  | 'star' 
  | 'arrow' 
  | 'bounce' 
  | 'pulse' 
  | 'wave'
  | 'clock'
  | 'earth';

export interface SpinnerProps {
  type?: SpinnerType;
  text?: string;
  color?: string;
  interval?: number;
  enabled?: boolean;
}

const SPINNER_FRAMES: Record<SpinnerType, string[]> = {
  dots: ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'],
  line: ['|', '/', '-', '\\'],
  pipe: ['┤', '┘', '┴', '└', '├', '┌', '┬', '┐'],
  star: ['✶', '✸', '✹', '✺', '✹', '✷'],
  arrow: ['←', '↖', '↑', '↗', '→', '↘', '↓', '↙'],
  bounce: ['⠁', '⠂', '⠄', '⠂'],
  pulse: ['●', '○', '●', '○'],
  wave: ['▁', '▃', '▄', '▅', '▆', '▇', '█', '▇', '▆', '▅', '▄', '▃'],
  clock: ['🕐', '🕑', '🕒', '🕓', '🕔', '🕕', '🕖', '🕗', '🕘', '🕙', '🕚', '🕛'],
  earth: ['🌍', '🌎', '🌏']
};

const DEFAULT_INTERVALS: Record<SpinnerType, number> = {
  dots: 80,
  line: 130,
  pipe: 100,
  star: 70,
  arrow: 120,
  bounce: 180,
  pulse: 200,
  wave: 60,
  clock: 100,
  earth: 180
};

export function Spinner({
  type = 'dots',
  text = '',
  color = 'cyan',
  interval,
  enabled = true
}: SpinnerProps) {
  const [frameIndex, setFrameIndex] = useState(0);
  
  const frames = SPINNER_FRAMES[type];
  const frameInterval = interval || DEFAULT_INTERVALS[type];

  useEffect(() => {
    if (!enabled) {
      return;
    }

    const timer = setInterval(() => {
      setFrameIndex(prevIndex => (prevIndex + 1) % frames.length);
    }, frameInterval);

    return () => clearInterval(timer);
  }, [frames.length, frameInterval, enabled]);

  if (!enabled) {
    return text ? <Text>{text}</Text> : null;
  }

  const currentFrame = frames[frameIndex];

  return (
    <Text color={color}>
      {currentFrame}
      {text && ` ${text}`}
    </Text>
  );
}

/**
 * Loading Spinner with customizable message
 */
export interface LoadingSpinnerProps extends SpinnerProps {
  message?: string;
  showElapsed?: boolean;
  startTime?: number;
}

export function LoadingSpinner({
  message = 'Loading...',
  showElapsed = false,
  startTime,
  ...spinnerProps
}: LoadingSpinnerProps) {
  const [elapsed, setElapsed] = useState(0);

  useEffect(() => {
    if (!showElapsed || !startTime) {
      return;
    }

    const timer = setInterval(() => {
      setElapsed(Date.now() - startTime);
    }, 100);

    return () => clearInterval(timer);
  }, [showElapsed, startTime]);

  const formatElapsed = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <Text>
      <Spinner {...spinnerProps} />
      <Text> {message}</Text>
      {showElapsed && elapsed > 0 && (
        <Text color="gray"> ({formatElapsed(elapsed)})</Text>
      )}
    </Text>
  );
}

/**
 * Multi-step progress spinner
 */
export interface ProgressSpinnerProps {
  steps: string[];
  currentStep: number;
  type?: SpinnerType;
  color?: string;
  completedColor?: string;
  enabled?: boolean;
}

export function ProgressSpinner({
  steps,
  currentStep,
  type = 'dots',
  color = 'cyan',
  completedColor = 'green',
  enabled = true
}: ProgressSpinnerProps) {
  return (
    <>
      {steps.map((step, index) => {
        const isCompleted = index < currentStep;
        const isCurrent = index === currentStep;
        const isPending = index > currentStep;

        return (
          <Text key={index}>
            {isCompleted && <Text color={completedColor}>✓ </Text>}
            {isCurrent && <Spinner type={type} color={color} enabled={enabled} />}
            {isPending && <Text color="gray">○ </Text>}
            <Text 
              color={isCompleted ? completedColor : isCurrent ? color : 'gray'}
              dimColor={isPending}
            >
              {isCurrent ? ' ' : ''}{step}
            </Text>
          </Text>
        );
      })}
    </>
  );
}

/**
 * Spinner with status indicator
 */
export interface StatusSpinnerProps extends SpinnerProps {
  status: 'loading' | 'success' | 'error' | 'warning';
  successText?: string;
  errorText?: string;
  warningText?: string;
}

export function StatusSpinner({
  status,
  text = '',
  successText,
  errorText,
  warningText,
  ...spinnerProps
}: StatusSpinnerProps) {
  switch (status) {
    case 'loading':
      return <Spinner {...spinnerProps} text={text} />;
    
    case 'success':
      return (
        <Text color="green">
          ✓ {successText || text || 'Success'}
        </Text>
      );
    
    case 'error':
      return (
        <Text color="red">
          ✗ {errorText || text || 'Error'}
        </Text>
      );
    
    case 'warning':
      return (
        <Text color="yellow">
          ⚠ {warningText || text || 'Warning'}
        </Text>
      );
    
    default:
      return <Spinner {...spinnerProps} text={text} />;
  }
}

/**
 * Animated dots for typing indicator
 */
export interface TypingIndicatorProps {
  color?: string;
  enabled?: boolean;
}

export function TypingIndicator({ color = 'gray', enabled = true }: TypingIndicatorProps) {
  const [dotCount, setDotCount] = useState(1);

  useEffect(() => {
    if (!enabled) {
      return;
    }

    const timer = setInterval(() => {
      setDotCount(prev => (prev % 3) + 1);
    }, 500);

    return () => clearInterval(timer);
  }, [enabled]);

  if (!enabled) {
    return null;
  }

  return (
    <Text color={color}>
      {'●'.repeat(dotCount)}{'○'.repeat(3 - dotCount)}
    </Text>
  );
}

/**
 * Pulsing indicator
 */
export interface PulseIndicatorProps {
  color?: string;
  character?: string;
  interval?: number;
  enabled?: boolean;
}

export function PulseIndicator({
  color = 'cyan',
  character = '●',
  interval = 600,
  enabled = true
}: PulseIndicatorProps) {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    if (!enabled) {
      return;
    }

    const timer = setInterval(() => {
      setVisible(prev => !prev);
    }, interval);

    return () => clearInterval(timer);
  }, [interval, enabled]);

  if (!enabled) {
    return null;
  }

  return (
    <Text color={color} dimColor={!visible}>
      {character}
    </Text>
  );
}

/**
 * Custom spinner with user-defined frames
 */
export interface CustomSpinnerProps {
  frames: string[];
  text?: string;
  color?: string;
  interval?: number;
  enabled?: boolean;
}

export function CustomSpinner({
  frames,
  text = '',
  color = 'cyan',
  interval = 100,
  enabled = true
}: CustomSpinnerProps) {
  const [frameIndex, setFrameIndex] = useState(0);

  useEffect(() => {
    if (!enabled || frames.length === 0) {
      return;
    }

    const timer = setInterval(() => {
      setFrameIndex(prevIndex => (prevIndex + 1) % frames.length);
    }, interval);

    return () => clearInterval(timer);
  }, [frames.length, interval, enabled]);

  if (!enabled || frames.length === 0) {
    return text ? <Text>{text}</Text> : null;
  }

  const currentFrame = frames[frameIndex];

  return (
    <Text color={color}>
      {currentFrame}
      {text && ` ${text}`}
    </Text>
  );
}
