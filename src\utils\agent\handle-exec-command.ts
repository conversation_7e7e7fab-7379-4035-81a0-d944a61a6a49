/**
 * Shell Command Execution Handler
 * 
 * Processes shell commands with platform adaptation, security validation,
 * and comprehensive output capture
 */

import { spawn } from 'child_process';
import { resolve } from 'path';
import { existsSync } from 'fs';
import { logAgentExecution, logError } from '../logger/log.js';
import { adaptCommandForPlatform } from './platform-commands.js';
import { exec as rawExec } from './sandbox/raw-exec.js';
import { execWithLandlock } from './sandbox/landlock.js';
import { execWithSeatbelt } from './sandbox/macos-seatbelt.js';
import type { ExecInput, ExecResult, AppConfig, ApprovalPolicy } from '../../types/index.js';

/**
 * Execute shell command with comprehensive handling
 */
export async function handleExecCommand(
  input: ExecInput,
  config: AppConfig,
  approvalPolicy: ApprovalPolicy,
  additionalWritableRoots: string[] = [],
  signal?: AbortSignal
): Promise<ExecResult> {
  const startTime = Date.now();
  
  try {
    logAgentExecution('exec_start', {
      command: input.command,
      workdir: input.workdir,
      timeout: input.timeout
    });

    // Validate input
    if (!input.command || input.command.length === 0) {
      throw new Error('No command provided');
    }

    // Resolve working directory
    const workdir = input.workdir ? resolve(input.workdir) : process.cwd();
    
    // Validate working directory
    if (!existsSync(workdir)) {
      throw new Error(`Working directory does not exist: ${workdir}`);
    }

    // Adapt command for platform
    const adaptedCommand = adaptCommandForPlatform(input.command);
    
    logAgentExecution('exec_adapted', {
      original: input.command,
      adapted: adaptedCommand,
      platform: process.platform
    });

    // Execute command with appropriate sandbox
    const execInput: ExecInput = {
      command: adaptedCommand,
      workdir,
      timeout: input.timeout || 30000
    };

    const result = await executeWithSandbox(
      execInput,
      config,
      approvalPolicy,
      additionalWritableRoots,
      signal
    );

    const duration = Date.now() - startTime;
    
    logAgentExecution('exec_complete', {
      command: adaptedCommand,
      exitCode: result.exitCode,
      duration,
      success: result.success
    });

    return {
      ...result,
      command: input.command, // Return original command
      workdir,
      duration
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    logError('Command execution failed', error instanceof Error ? error : new Error(errorMessage));

    return {
      success: false,
      output: '',
      error: errorMessage,
      exitCode: -1,
      command: input.command,
      workdir: input.workdir || process.cwd(),
      duration
    };
  }
}

/**
 * Execute command with appropriate sandbox based on platform and approval policy
 */
async function executeWithSandbox(
  input: ExecInput,
  config: AppConfig,
  approvalPolicy: ApprovalPolicy,
  additionalWritableRoots: string[],
  signal?: AbortSignal
): Promise<Omit<ExecResult, 'command' | 'workdir' | 'duration'>> {
  // Determine sandbox strategy based on approval policy and platform
  const useSandbox = approvalPolicy === 'full-auto' || config.useSandbox !== false;

  if (!useSandbox) {
    // Use raw execution for suggest/auto-edit modes or when sandboxing is disabled
    return await rawExec(input, config);
  }

  // Try platform-specific sandboxing
  try {
    if (process.platform === 'linux') {
      // Try Landlock on Linux
      const { testLandlock } = await import('./sandbox/landlock.js');
      const landlockTest = await testLandlock();

      if (landlockTest.available) {
        logAgentExecution('sandbox_selected', { type: 'landlock', platform: 'linux' });
        return await execWithLandlock(input, config, additionalWritableRoots);
      }
    } else if (process.platform === 'darwin') {
      // Try Seatbelt on macOS
      const { testSeatbelt } = await import('./sandbox/macos-seatbelt.js');
      const seatbeltTest = await testSeatbelt();

      if (seatbeltTest.available) {
        logAgentExecution('sandbox_selected', { type: 'seatbelt', platform: 'darwin' });
        return await execWithSeatbelt(input, config);
      }
    }

    // Fallback to raw execution if sandboxing is not available
    logAgentExecution('sandbox_fallback', {
      reason: 'Platform sandboxing not available',
      platform: process.platform
    });

    return await rawExec(input, config);

  } catch (error) {
    // Fallback to raw execution on sandbox errors
    logError('Sandbox execution failed, falling back to raw execution',
      error instanceof Error ? error : new Error(String(error)));

    return await rawExec(input, config);
  }
}

/**
 * Execute command using child_process.spawn (legacy function for compatibility)
 */
async function executeCommand(
  command: string[],
  workdir: string,
  timeout: number,
  signal?: AbortSignal
): Promise<Omit<ExecResult, 'command' | 'workdir' | 'duration'>> {
  return new Promise((resolve, reject) => {
    const [cmd, ...args] = command;
    
    // Create child process
    const child = spawn(cmd, args, {
      cwd: workdir,
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: process.platform === 'win32',
      env: {
        ...process.env,
        // Ensure UTF-8 encoding
        LANG: 'en_US.UTF-8',
        LC_ALL: 'en_US.UTF-8'
      }
    });

    let stdout = '';
    let stderr = '';
    let timeoutId: NodeJS.Timeout | null = null;
    let isResolved = false;

    // Handle abort signal
    if (signal) {
      signal.addEventListener('abort', () => {
        if (!isResolved) {
          child.kill('SIGTERM');
          setTimeout(() => {
            if (!child.killed) {
              child.kill('SIGKILL');
            }
          }, 5000);
        }
      });
    }

    // Set timeout
    if (timeout > 0) {
      timeoutId = setTimeout(() => {
        if (!isResolved) {
          child.kill('SIGTERM');
          setTimeout(() => {
            if (!child.killed) {
              child.kill('SIGKILL');
            }
          }, 5000);
        }
      }, timeout);
    }

    // Collect stdout
    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    // Collect stderr
    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    // Handle process completion
    child.on('close', (code, signal) => {
      if (isResolved) return;
      isResolved = true;

      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      const exitCode = code !== null ? code : (signal ? -1 : 0);
      const success = exitCode === 0;
      
      // Combine stdout and stderr for output
      let output = stdout;
      if (stderr) {
        if (output) {
          output += '\n--- stderr ---\n' + stderr;
        } else {
          output = stderr;
        }
      }

      resolve({
        success,
        output: output.trim(),
        error: success ? undefined : stderr.trim() || `Process exited with code ${exitCode}`,
        exitCode
      });
    });

    // Handle process errors
    child.on('error', (error) => {
      if (isResolved) return;
      isResolved = true;

      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      reject(new Error(`Failed to start process: ${error.message}`));
    });

    // Handle spawn errors
    child.on('spawn', () => {
      logAgentExecution('process_spawned', { pid: child.pid });
    });
  });
}

/**
 * Validate command safety
 */
export function validateCommandSafety(
  command: string[],
  workdir: string,
  config: AppConfig
): {
  safe: boolean;
  warnings: string[];
  blockers: string[];
} {
  const warnings: string[] = [];
  const blockers: string[] = [];
  
  const commandString = command.join(' ').toLowerCase();
  const dangerousCommands = config.dangerousCommands || [];
  const commandName = command[0]?.toLowerCase();

  // Check for dangerous commands
  if (dangerousCommands.includes(commandName)) {
    warnings.push(`Command '${commandName}' is marked as dangerous`);
  }

  // Check for dangerous patterns
  const dangerousPatterns = [
    { pattern: /rm\s+-rf\s+\//, message: 'Attempting to delete root directory', blocker: true },
    { pattern: /sudo\s+rm\s+-rf/, message: 'Privileged recursive delete', blocker: true },
    { pattern: /dd\s+if=.*of=\/dev/, message: 'Writing to device file', blocker: true },
    { pattern: /mkfs/, message: 'Formatting filesystem', blocker: true },
    { pattern: /chmod\s+777/, message: 'Setting overly permissive permissions' },
    { pattern: /curl.*\|.*sh/, message: 'Downloading and executing script' },
    { pattern: /wget.*\|.*sh/, message: 'Downloading and executing script' },
    { pattern: /sudo/, message: 'Command requires elevated privileges' }
  ];

  for (const { pattern, message, blocker } of dangerousPatterns) {
    if (pattern.test(commandString)) {
      if (blocker) {
        blockers.push(message);
      } else {
        warnings.push(message);
      }
    }
  }

  // Check working directory
  if (workdir === '/' || workdir === 'C:\\') {
    warnings.push('Command will execute in root directory');
  }

  return {
    safe: blockers.length === 0,
    warnings,
    blockers
  };
}

/**
 * Format command output for display
 */
export function formatCommandOutput(result: ExecResult): string {
  const lines: string[] = [];
  
  // Command header
  lines.push(`$ ${result.command.join(' ')}`);
  
  if (result.workdir !== process.cwd()) {
    lines.push(`Working directory: ${result.workdir}`);
  }
  
  // Output
  if (result.output) {
    lines.push('');
    lines.push(result.output);
  }
  
  // Footer with status
  lines.push('');
  if (result.success) {
    lines.push(`✓ Command completed successfully (${result.duration}ms)`);
  } else {
    lines.push(`✗ Command failed with exit code ${result.exitCode} (${result.duration}ms)`);
    if (result.error && result.error !== result.output) {
      lines.push(`Error: ${result.error}`);
    }
  }
  
  return lines.join('\n');
}

/**
 * Estimate command execution time
 */
export function estimateExecutionTime(command: string[]): number {
  const commandName = command[0]?.toLowerCase();
  
  // Rough estimates in milliseconds
  const timeEstimates: Record<string, number> = {
    'ls': 100,
    'cat': 200,
    'grep': 500,
    'find': 2000,
    'git': 1000,
    'npm': 5000,
    'pip': 3000,
    'curl': 2000,
    'wget': 3000,
    'make': 10000,
    'cargo': 15000,
    'mvn': 20000,
    'gradle': 25000
  };
  
  return timeEstimates[commandName] || 1000; // Default 1 second
}

/**
 * Check if command modifies files
 */
export function commandModifiesFiles(command: string[]): boolean {
  const commandName = command[0]?.toLowerCase();
  const modifyingCommands = [
    'rm', 'mv', 'cp', 'mkdir', 'rmdir', 'touch', 'chmod', 'chown',
    'git', 'npm', 'pip', 'make', 'cargo', 'mvn', 'gradle',
    'del', 'move', 'copy', 'md', 'rd'
  ];
  
  return modifyingCommands.includes(commandName);
}

/**
 * Get command description for user
 */
export function getCommandDescription(command: string[]): string {
  const commandName = command[0]?.toLowerCase();
  const args = command.slice(1);
  
  const descriptions: Record<string, (args: string[]) => string> = {
    'ls': () => 'List directory contents',
    'cat': (args) => `Display contents of ${args.join(', ')}`,
    'grep': (args) => `Search for "${args[0]}" in files`,
    'find': () => 'Search for files and directories',
    'rm': (args) => `Delete ${args.join(', ')}`,
    'mv': (args) => `Move ${args[0]} to ${args[1]}`,
    'cp': (args) => `Copy ${args[0]} to ${args[1]}`,
    'mkdir': (args) => `Create directory ${args.join(', ')}`,
    'git': (args) => `Git: ${args.join(' ')}`,
    'npm': (args) => `NPM: ${args.join(' ')}`,
    'pip': (args) => `Python package: ${args.join(' ')}`
  };
  
  const describer = descriptions[commandName];
  return describer ? describer(args) : `Execute: ${command.join(' ')}`;
}
