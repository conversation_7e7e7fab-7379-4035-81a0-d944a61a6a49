/**
 * Auto-completion System
 * 
 * Provides intelligent auto-completion for commands, file paths, and slash commands
 * Supports fuzzy matching and context-aware suggestions
 */

import { readdirSync, statSync, existsSync } from 'fs';
import { join, dirname, basename, extname } from 'path';
import { getSlashCommandNames } from './slash-commands.js';
import { getHistory } from './storage/command-history.js';

export interface CompletionResult {
  suggestions: string[];
  prefix: string;
  hasMore: boolean;
  type: 'command' | 'file' | 'slash' | 'history' | 'mixed';
}

export interface CompletionOptions {
  maxSuggestions?: number;
  includeHidden?: boolean;
  fuzzyMatch?: boolean;
  contextAware?: boolean;
}

/**
 * Get auto-completion suggestions for input
 */
export function getCompletions(
  input: string,
  cursorPosition: number = input.length,
  options: CompletionOptions = {}
): CompletionResult {
  const maxSuggestions = options.maxSuggestions || 10;
  const includeHidden = options.includeHidden || false;
  const fuzzyMatch = options.fuzzyMatch || true;

  // Extract the word at cursor position
  const { word, prefix, type } = extractWordAtCursor(input, cursorPosition);

  let suggestions: string[] = [];

  switch (type) {
    case 'slash':
      suggestions = getSlashCommandCompletions(word, fuzzyMatch);
      break;
    case 'file':
      suggestions = getFileCompletions(word, includeHidden);
      break;
    case 'command':
      suggestions = getCommandCompletions(word, fuzzyMatch);
      break;
    default:
      // Mixed completion - try all types
      suggestions = [
        ...getSlashCommandCompletions(word, fuzzyMatch),
        ...getFileCompletions(word, includeHidden),
        ...getCommandCompletions(word, fuzzyMatch)
      ];
  }

  // Remove duplicates and limit results
  suggestions = [...new Set(suggestions)];
  
  if (fuzzyMatch) {
    suggestions = fuzzyFilter(suggestions, word);
  }

  const hasMore = suggestions.length > maxSuggestions;
  suggestions = suggestions.slice(0, maxSuggestions);

  return {
    suggestions,
    prefix,
    hasMore,
    type: type || 'mixed'
  };
}

/**
 * Extract word at cursor position and determine completion type
 */
function extractWordAtCursor(
  input: string,
  cursorPosition: number
): {
  word: string;
  prefix: string;
  type: 'command' | 'file' | 'slash' | null;
} {
  // Find word boundaries
  let start = cursorPosition;
  let end = cursorPosition;

  // Move start backward to find word start
  while (start > 0 && !/\s/.test(input[start - 1])) {
    start--;
  }

  // Move end forward to find word end
  while (end < input.length && !/\s/.test(input[end])) {
    end++;
  }

  const word = input.slice(start, end);
  const prefix = input.slice(0, start);

  // Determine completion type
  let type: 'command' | 'file' | 'slash' | null = null;

  if (word.startsWith('/')) {
    type = 'slash';
  } else if (word.startsWith('@') || word.includes('/') || word.includes('.')) {
    type = 'file';
  } else if (prefix.trim() === '' || prefix.trim().split(/\s+/).length === 1) {
    type = 'command';
  }

  return { word, prefix, type };
}

/**
 * Get slash command completions
 */
function getSlashCommandCompletions(
  input: string,
  fuzzyMatch: boolean = true
): string[] {
  const commands = getSlashCommandNames();
  const query = input.startsWith('/') ? input : `/${input}`;

  if (!fuzzyMatch) {
    return commands.filter(cmd => cmd.toLowerCase().startsWith(query.toLowerCase()));
  }

  return commands;
}

/**
 * Get file path completions
 */
function getFileCompletions(
  input: string,
  includeHidden: boolean = false
): string[] {
  try {
    // Handle @file syntax
    let path = input;
    if (path.startsWith('@')) {
      path = path.slice(1);
    }

    // Determine directory and filename
    let dir: string;
    let filename: string;

    if (path.includes('/') || path.includes('\\')) {
      dir = dirname(path);
      filename = basename(path);
    } else {
      dir = '.';
      filename = path;
    }

    // Resolve directory
    const resolvedDir = dir === '.' ? process.cwd() : join(process.cwd(), dir);

    if (!existsSync(resolvedDir)) {
      return [];
    }

    // Get directory contents
    const entries = readdirSync(resolvedDir);
    const suggestions: string[] = [];

    for (const entry of entries) {
      // Skip hidden files unless requested
      if (!includeHidden && entry.startsWith('.')) {
        continue;
      }

      // Filter by filename prefix
      if (filename && !entry.toLowerCase().startsWith(filename.toLowerCase())) {
        continue;
      }

      const fullPath = join(resolvedDir, entry);
      const stats = statSync(fullPath);

      if (stats.isDirectory()) {
        suggestions.push(`${entry}/`);
      } else {
        suggestions.push(entry);
      }
    }

    // Add path prefix back
    const pathPrefix = dir === '.' ? '' : `${dir}/`;
    return suggestions.map(s => `${pathPrefix}${s}`);

  } catch (error) {
    return [];
  }
}

/**
 * Get command completions from history
 */
function getCommandCompletions(
  input: string,
  fuzzyMatch: boolean = true
): string[] {
  try {
    const history = getHistory();
    const commands = history.map(entry => entry.command);
    
    // Get unique commands
    const uniqueCommands = [...new Set(commands)];

    if (!fuzzyMatch) {
      return uniqueCommands.filter(cmd => 
        cmd.toLowerCase().startsWith(input.toLowerCase())
      );
    }

    return uniqueCommands;

  } catch (error) {
    return [];
  }
}

/**
 * Fuzzy filter suggestions
 */
function fuzzyFilter(suggestions: string[], query: string): string[] {
  if (!query) {
    return suggestions;
  }

  const queryLower = query.toLowerCase();
  const scored = suggestions.map(suggestion => ({
    suggestion,
    score: fuzzyScore(suggestion.toLowerCase(), queryLower)
  }));

  return scored
    .filter(item => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .map(item => item.suggestion);
}

/**
 * Calculate fuzzy match score
 */
function fuzzyScore(text: string, query: string): number {
  if (text === query) return 100;
  if (text.startsWith(query)) return 90;
  if (text.includes(query)) return 70;

  // Character-by-character fuzzy matching
  let score = 0;
  let textIndex = 0;
  let queryIndex = 0;

  while (textIndex < text.length && queryIndex < query.length) {
    if (text[textIndex] === query[queryIndex]) {
      score += 1;
      queryIndex++;
    }
    textIndex++;
  }

  // Bonus for matching all characters
  if (queryIndex === query.length) {
    score += 10;
  }

  // Penalty for length difference
  score -= Math.abs(text.length - query.length) * 0.1;

  return Math.max(0, score);
}

/**
 * Get file type completions
 */
export function getFileTypeCompletions(
  directory: string = '.',
  extensions: string[] = []
): string[] {
  try {
    const resolvedDir = directory === '.' ? process.cwd() : join(process.cwd(), directory);
    
    if (!existsSync(resolvedDir)) {
      return [];
    }

    const entries = readdirSync(resolvedDir);
    const suggestions: string[] = [];

    for (const entry of entries) {
      const fullPath = join(resolvedDir, entry);
      const stats = statSync(fullPath);

      if (stats.isFile()) {
        const ext = extname(entry).toLowerCase();
        
        if (extensions.length === 0 || extensions.includes(ext)) {
          suggestions.push(entry);
        }
      }
    }

    return suggestions.sort();

  } catch (error) {
    return [];
  }
}

/**
 * Get directory completions
 */
export function getDirectoryCompletions(
  basePath: string = '.',
  includeHidden: boolean = false
): string[] {
  try {
    const resolvedDir = basePath === '.' ? process.cwd() : join(process.cwd(), basePath);
    
    if (!existsSync(resolvedDir)) {
      return [];
    }

    const entries = readdirSync(resolvedDir);
    const suggestions: string[] = [];

    for (const entry of entries) {
      if (!includeHidden && entry.startsWith('.')) {
        continue;
      }

      const fullPath = join(resolvedDir, entry);
      const stats = statSync(fullPath);

      if (stats.isDirectory()) {
        suggestions.push(`${entry}/`);
      }
    }

    return suggestions.sort();

  } catch (error) {
    return [];
  }
}

/**
 * Auto-completion manager class
 */
export class AutoCompleteManager {
  private cache = new Map<string, CompletionResult>();
  private cacheTimeout = 5000; // 5 seconds

  /**
   * Get completions with caching
   */
  getCompletions(
    input: string,
    cursorPosition?: number,
    options?: CompletionOptions
  ): CompletionResult {
    const cacheKey = `${input}:${cursorPosition}:${JSON.stringify(options)}`;
    const cached = this.cache.get(cacheKey);

    if (cached) {
      return cached;
    }

    const result = getCompletions(input, cursorPosition, options);
    
    // Cache result
    this.cache.set(cacheKey, result);
    
    // Clear cache after timeout
    setTimeout(() => {
      this.cache.delete(cacheKey);
    }, this.cacheTimeout);

    return result;
  }

  /**
   * Clear completion cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache size
   */
  getCacheSize(): number {
    return this.cache.size;
  }
}

// Global auto-complete manager
export const autoCompleteManager = new AutoCompleteManager();

/**
 * Get file system suggestions for a query
 */
export function getFileSystemSuggestions(
  query: string,
  basePath: string = process.cwd(),
  maxResults: number = 10
): string[] {
  try {
    // Handle @file syntax
    let searchPath = query;
    if (searchPath.startsWith('@')) {
      searchPath = searchPath.slice(1);
    }

    // Determine directory and filename
    let dir: string;
    let filename: string;

    if (searchPath.includes('/') || searchPath.includes('\\')) {
      dir = dirname(searchPath);
      filename = basename(searchPath);
    } else {
      dir = '.';
      filename = searchPath;
    }

    // Resolve directory
    const resolvedDir = dir === '.' ? basePath : join(basePath, dir);

    if (!existsSync(resolvedDir)) {
      return [];
    }

    // Get directory contents
    const entries = readdirSync(resolvedDir);
    const suggestions: string[] = [];

    for (const entry of entries) {
      // Skip hidden files
      if (entry.startsWith('.')) {
        continue;
      }

      // Filter by filename prefix
      if (filename && !entry.toLowerCase().includes(filename.toLowerCase())) {
        continue;
      }

      const fullPath = join(resolvedDir, entry);
      const stats = statSync(fullPath);

      if (stats.isDirectory()) {
        suggestions.push(`${entry}/`);
      } else {
        suggestions.push(entry);
      }

      // Limit results
      if (suggestions.length >= maxResults) {
        break;
      }
    }

    // Add path prefix back if needed
    const pathPrefix = dir === '.' ? '' : `${dir}/`;
    return suggestions.map(s => `${pathPrefix}${s}`);

  } catch (error) {
    return [];
  }
}
