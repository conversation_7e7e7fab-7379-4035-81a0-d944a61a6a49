/**
 * Comprehensive Logging System
 * 
 * Multi-platform logging with async queue processing
 * Supports conditional logging and performance monitoring
 */

import { writeFileSync, existsSync, mkdirSync, symlinkSync, unlinkSync } from 'fs';
import { join, dirname } from 'path';
import { tmpdir, homedir } from 'os';

export interface Logger {
  log(message: string, details?: any): void;
  error(message: string, error?: Error): void;
  warn(message: string): void;
  info(message: string, details?: any): void;
  debug(message: string): void;
}

class AsyncLogger implements Logger {
  private queue: string[] = [];
  private isWriting: boolean = false;
  private logPath: string;
  private enabled: boolean;

  constructor(logPath: string, enabled: boolean = false) {
    this.logPath = logPath;
    this.enabled = enabled;
    
    if (this.enabled) {
      this.ensureLogDirectory();
      this.createSymlink();
    }
  }

  private ensureLogDirectory(): void {
    const logDir = dirname(this.logPath);
    if (!existsSync(logDir)) {
      mkdirSync(logDir, { recursive: true });
    }
  }

  private createSymlink(): void {
    try {
      const logDir = dirname(this.logPath);
      const symlinkPath = join(logDir, 'kritrima-ai-latest.log');
      
      // Remove existing symlink
      if (existsSync(symlinkPath)) {
        unlinkSync(symlinkPath);
      }
      
      // Create new symlink
      symlinkSync(this.logPath, symlinkPath);
    } catch (error) {
      // Symlink creation failed, continue without it
    }
  }

  private now(): string {
    return new Date().toISOString();
  }

  private formatMessage(level: string, message: string, error?: Error): string {
    let formatted = `[${this.now()}] [${level.toUpperCase()}] ${message}`;
    
    if (error) {
      formatted += `\nError: ${error.message}`;
      if (error.stack) {
        formatted += `\nStack: ${error.stack}`;
      }
    }
    
    return formatted + '\n';
  }

  private async maybeWrite(): Promise<void> {
    if (!this.enabled || this.isWriting || this.queue.length === 0) {
      return;
    }

    this.isWriting = true;
    
    const entries = this.queue.splice(0);

    try {
      const content = entries.join('');

      // Use sync write for simplicity and reliability
      writeFileSync(this.logPath, content, { flag: 'a' });
    } catch (error) {
      // Log write failed, restore queue
      this.queue.unshift(...entries);
    } finally {
      this.isWriting = false;
      
      // Process remaining queue
      if (this.queue.length > 0) {
        setImmediate(() => this.maybeWrite());
      }
    }
  }

  log(message: string, details?: any): void {
    if (!this.enabled) return;

    let fullMessage = message;
    if (details) {
      fullMessage += ` - ${JSON.stringify(details)}`;
    }

    const entry = this.formatMessage('info', fullMessage);
    this.queue.push(entry);
    this.maybeWrite();
  }

  error(message: string, error?: Error): void {
    if (!this.enabled) return;
    
    const entry = this.formatMessage('error', message, error);
    this.queue.push(entry);
    this.maybeWrite();
  }

  warn(message: string): void {
    if (!this.enabled) return;
    
    const entry = this.formatMessage('warn', message);
    this.queue.push(entry);
    this.maybeWrite();
  }

  info(message: string, details?: any): void {
    if (!this.enabled) return;

    let fullMessage = message;
    if (details) {
      fullMessage += ` - ${JSON.stringify(details)}`;
    }

    const entry = this.formatMessage('info', fullMessage);
    this.queue.push(entry);
    this.maybeWrite();
  }

  debug(message: string): void {
    if (!this.enabled) return;
    
    const entry = this.formatMessage('debug', message);
    this.queue.push(entry);
    this.maybeWrite();
  }
}

// Global logger instance
let globalLogger: Logger | null = null;

/**
 * Initialize logger with platform-specific paths
 */
function initializeLogger(): Logger {
  if (globalLogger) {
    return globalLogger;
  }

  const enabled = isLoggingEnabled();
  let logPath: string;

  // Determine platform-specific log path
  if (process.platform === 'darwin' || process.platform === 'win32') {
    // macOS/Windows: Use temp directory
    const logDir = join(tmpdir(), 'kritrima-ai');
    logPath = join(logDir, `kritrima-ai-cli-${Date.now()}.log`);
  } else {
    // Linux: Use user's local directory
    const logDir = join(homedir(), '.local', 'kritrima-ai');
    logPath = join(logDir, `kritrima-ai-cli-${Date.now()}.log`);
  }

  globalLogger = new AsyncLogger(logPath, enabled);
  return globalLogger;
}

/**
 * Check if logging is enabled
 */
export function isLoggingEnabled(): boolean {
  return process.env.DEBUG === '1' || 
         process.env.KRITRIMA_AI_DEBUG === 'true' ||
         process.env.KRITRIMA_AI_ENABLE_LOGGING === 'true';
}

/**
 * Get the global logger instance
 */
export function getLogger(): Logger {
  return initializeLogger();
}

/**
 * Log a message
 */
export function log(message: string): void {
  getLogger().log(message);
}

/**
 * Log an error
 */
export function logError(message: string, error?: Error): void {
  getLogger().error(message, error);
}

/**
 * Log a warning
 */
export function logWarn(message: string): void {
  getLogger().warn(message);
}

/**
 * Log an info message
 */
export function logInfo(message: string): void {
  getLogger().info(message);
}

/**
 * Log a debug message
 */
export function logDebug(message: string): void {
  getLogger().debug(message);
}

/**
 * Performance monitoring for UI rendering
 */
export class PerformanceMonitor {
  private frameCount = 0;
  private lastTime = Date.now();
  private fpsHistory: number[] = [];

  logFrame(): void {
    if (!isLoggingEnabled()) return;

    this.frameCount++;
    const now = Date.now();
    const elapsed = now - this.lastTime;

    if (elapsed >= 1000) { // Every second
      const fps = Math.round((this.frameCount * 1000) / elapsed);
      this.fpsHistory.push(fps);
      
      // Keep only last 10 measurements
      if (this.fpsHistory.length > 10) {
        this.fpsHistory.shift();
      }

      logDebug(`UI FPS: ${fps} (avg: ${Math.round(this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length)})`);
      
      this.frameCount = 0;
      this.lastTime = now;
    }
  }

  getAverageFPS(): number {
    if (this.fpsHistory.length === 0) return 0;
    return Math.round(this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length);
  }
}

// Global performance monitor
export const performanceMonitor = new PerformanceMonitor();

/**
 * Log agent loop execution
 */
export function logAgentExecution(
  action: string,
  details: any,
  duration?: number
): void {
  if (!isLoggingEnabled()) return;

  let message = `Agent: ${action}`;
  if (duration !== undefined) {
    message += ` (${duration}ms)`;
  }
  
  if (details) {
    message += ` - ${JSON.stringify(details)}`;
  }

  logDebug(message);
}

/**
 * Log network requests
 */
export function logNetworkRequest(
  method: string,
  url: string,
  status?: number,
  duration?: number
): void {
  if (!isLoggingEnabled()) return;

  let message = `Network: ${method} ${url}`;
  if (status !== undefined) {
    message += ` -> ${status}`;
  }
  if (duration !== undefined) {
    message += ` (${duration}ms)`;
  }

  logDebug(message);
}

/**
 * Get recent log entries for bug reports
 */
export function getRecentLogs(count: number = 50): string[] {
  const logger = getLogger();

  // If logger has a method to get recent logs, use it
  if (typeof (logger as any).getRecentLogs === 'function') {
    return (logger as any).getRecentLogs(count);
  }

  // Otherwise return empty array (logs might be written to file)
  return [];
}
