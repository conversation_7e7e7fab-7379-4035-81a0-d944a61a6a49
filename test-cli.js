#!/usr/bin/env node

/**
 * Test Script for Kritrima AI CLI
 * 
 * Basic functionality test to verify the CLI works correctly
 */

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';

const CLI_PATH = join(process.cwd(), 'dist', 'cli.js');

console.log('🧪 Testing Kritrima AI CLI...\n');

// Check if CLI is built
if (!existsSync(CLI_PATH)) {
  console.error('❌ CLI not found at:', CLI_PATH);
  console.error('Please run: npm run build');
  process.exit(1);
}

console.log('✅ CLI binary found');

// Test 1: Version check
console.log('\n📋 Test 1: Version check');
try {
  const versionProcess = spawn('node', [CLI_PATH, '--version'], {
    stdio: 'pipe'
  });

  let output = '';
  versionProcess.stdout.on('data', (data) => {
    output += data.toString();
  });

  versionProcess.on('close', (code) => {
    if (code === 0) {
      console.log('✅ Version check passed:', output.trim());
    } else {
      console.log('❌ Version check failed with code:', code);
    }
  });
} catch (error) {
  console.log('❌ Version check error:', error.message);
}

// Test 2: Help command
console.log('\n📋 Test 2: Help command');
try {
  const helpProcess = spawn('node', [CLI_PATH, '--help'], {
    stdio: 'pipe'
  });

  let output = '';
  helpProcess.stdout.on('data', (data) => {
    output += data.toString();
  });

  helpProcess.on('close', (code) => {
    if (code === 0 && output.includes('Kritrima AI CLI')) {
      console.log('✅ Help command passed');
    } else {
      console.log('❌ Help command failed');
    }
  });
} catch (error) {
  console.log('❌ Help command error:', error.message);
}

// Test 3: List providers
console.log('\n📋 Test 3: List providers');
try {
  const providersProcess = spawn('node', [CLI_PATH, '--list-providers'], {
    stdio: 'pipe'
  });

  let output = '';
  providersProcess.stdout.on('data', (data) => {
    output += data.toString();
  });

  providersProcess.on('close', (code) => {
    if (code === 0 && output.includes('Available AI Providers')) {
      console.log('✅ List providers passed');
    } else {
      console.log('❌ List providers failed');
    }
  });
} catch (error) {
  console.log('❌ List providers error:', error.message);
}

// Test 4: Single-pass mode (without API key)
console.log('\n📋 Test 4: Single-pass mode (dry run)');
try {
  const singlePassProcess = spawn('node', [CLI_PATH, 'test prompt'], {
    stdio: 'pipe',
    timeout: 5000
  });

  let output = '';
  let errorOutput = '';
  
  singlePassProcess.stdout.on('data', (data) => {
    output += data.toString();
  });

  singlePassProcess.stderr.on('data', (data) => {
    errorOutput += data.toString();
  });

  singlePassProcess.on('close', (code) => {
    // Expect this to fail due to missing API key, but should show proper error
    if (errorOutput.includes('API key') || errorOutput.includes('OPENAI_API_KEY')) {
      console.log('✅ Single-pass mode shows proper API key error');
    } else {
      console.log('❌ Single-pass mode unexpected behavior');
      console.log('Output:', output);
      console.log('Error:', errorOutput);
    }
  });
} catch (error) {
  console.log('❌ Single-pass mode error:', error.message);
}

console.log('\n🎉 Test suite completed!');
console.log('\n📝 To fully test the CLI:');
console.log('1. Set up API keys (e.g., export OPENAI_API_KEY=your_key)');
console.log('2. Run: node dist/cli.js --test-connection');
console.log('3. Run: node dist/cli.js "Hello, world!"');
console.log('4. Run: node dist/cli.js (for interactive mode)');
